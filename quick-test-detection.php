<?php
/**
 * Quick test to verify country detection is working
 */

// Include WordPress
require_once('wp-config.php');

// Check if we're in admin and user has permissions
if (!current_user_can('manage_woocommerce')) {
    wp_die('Access denied - you need WooCommerce management permissions');
}

echo "<h1>Quick Country Detection Test</h1>";

// Test 1: Check if classes exist
echo "<h2>1. Class Availability Check</h2>";
$classes_to_check = [
    'PBC_Core',
    'PBC_Country_Detector', 
    'PBC_Admin',
    'PBC_Database'
];

foreach ($classes_to_check as $class) {
    $exists = class_exists($class);
    echo "<p><strong>{$class}:</strong> " . ($exists ? '✅ Available' : '❌ Missing') . "</p>";
}

// Test 2: Initialize core components
echo "<h2>2. Core Components Test</h2>";
try {
    $pbc_core = PBC_Core::get_instance();
    echo "<p>✅ PBC_Core initialized successfully</p>";
    
    $country_detector = new PBC_Country_Detector($pbc_core->database);
    echo "<p>✅ PBC_Country_Detector initialized successfully</p>";
    
    $admin = new PBC_Admin($pbc_core->database, $pbc_core->logger, $pbc_core->error_handler);
    echo "<p>✅ PBC_Admin initialized successfully</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error initializing components: " . $e->getMessage() . "</p>";
}

// Test 3: Database table check
echo "<h2>3. Database Table Check</h2>";
global $wpdb;
$table_name = $wpdb->prefix . 'pbc_country_cache';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
echo "<p><strong>Cache Table ({$table_name}):</strong> " . ($table_exists ? '✅ Exists' : '❌ Missing') . "</p>";

if ($table_exists) {
    $row_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    echo "<p><strong>Current Records:</strong> {$row_count}</p>";
}

// Test 4: Country detection functionality
echo "<h2>4. Country Detection Test</h2>";
try {
    // Start session for caching
    if (!session_id()) {
        session_start();
    }
    
    $detected_country = $country_detector->detect_country('auto');
    echo "<p><strong>Auto Detection Result:</strong> {$detected_country}</p>";
    
    // Test IP detection with a known IP
    $test_ip = '*******';
    $ip_result = $country_detector->get_country_from_ip($test_ip);
    echo "<p><strong>IP Detection ({$test_ip}):</strong> " . ($ip_result ?: 'Failed') . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error in country detection: " . $e->getMessage() . "</p>";
}

// Test 5: Statistics check
echo "<h2>5. Statistics Check</h2>";
try {
    $stats = $admin->get_detection_statistics();
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Total Detections</td><td>" . $stats['total_detections'] . "</td></tr>";
    echo "<tr><td>IP Detections</td><td>" . $stats['ip_detections'] . " (" . $stats['ip_percentage'] . "%)</td></tr>";
    echo "<tr><td>Address Detections</td><td>" . $stats['address_detections'] . " (" . $stats['address_percentage'] . "%)</td></tr>";
    echo "<tr><td>Cache Hit Rate</td><td>" . $stats['cache_hit_rate'] . "%</td></tr>";
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ Error getting statistics: " . $e->getMessage() . "</p>";
}

// Test 6: AJAX handler registration check
echo "<h2>6. AJAX Handler Check</h2>";
$ajax_actions = [
    'pbc_test_ip_detection',
    'pbc_test_user_detection'
];

foreach ($ajax_actions as $action) {
    $has_action = has_action("wp_ajax_{$action}");
    echo "<p><strong>{$action}:</strong> " . ($has_action ? '✅ Registered' : '❌ Not registered') . "</p>";
}

// Test 7: Add some test data if table is empty
if ($table_exists && $row_count == 0) {
    echo "<h2>7. Adding Test Data</h2>";
    
    $test_data = [
        ['session' => 'test_' . time() . '_1', 'ip' => '*******', 'country' => 'US', 'method' => 'ip'],
        ['session' => 'test_' . time() . '_2', 'ip' => '*******', 'country' => 'US', 'method' => 'ip'],
        ['session' => 'test_' . time() . '_3', 'ip' => '*************', 'country' => 'GB', 'method' => 'billing'],
    ];
    
    foreach ($test_data as $data) {
        $result = $pbc_core->database->save_country_cache(
            $data['session'],
            $data['ip'],
            $data['country'],
            $data['method']
        );
        
        echo "<p>Added test data: {$data['ip']} -> {$data['country']} (" . ($result ? '✅' : '❌') . ")</p>";
    }
    
    // Refresh statistics
    echo "<h3>Updated Statistics:</h3>";
    $updated_stats = $admin->get_detection_statistics();
    echo "<p><strong>Total Detections:</strong> " . $updated_stats['total_detections'] . "</p>";
}

echo "<h2>8. Next Steps</h2>";
echo "<p>1. Go to <a href='" . admin_url('admin.php?page=pbc-country-detection') . "'>Country Detection Settings</a></p>";
echo "<p>2. Try the 'Test IP Detection' and 'Test User Detection' buttons</p>";
echo "<p>3. Check browser console (F12) for debug messages</p>";
echo "<p>4. Statistics should now show data if test data was added</p>";

echo "<h2>9. Debug Information</h2>";
echo "<p><strong>WordPress Version:</strong> " . get_bloginfo('version') . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Current User:</strong> " . wp_get_current_user()->user_login . "</p>";
echo "<p><strong>Session ID:</strong> " . (session_id() ?: 'None') . "</p>";
echo "<p><strong>Current IP:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "</p>";
?>