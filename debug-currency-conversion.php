<?php
/**
 * Debug currency conversion functionality
 */

// Load WordPress environment
if (!defined('ABSPATH')) {
    require_once(dirname(__FILE__) . '/../../../wp-load.php');
}

// Check permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Load required classes
require_once(dirname(__FILE__) . '/includes/class-pbc-autoloader.php');

echo "<h1>Currency Conversion Debug</h1>";

// Test 1: Check if currency conversion is enabled
$currency_conversion_enabled = get_option('pbc_enable_currency_conversion', false);
echo "<h2>1. Settings Check</h2>";
echo "Currency Conversion Enabled: " . ($currency_conversion_enabled ? 'YES' : 'NO') . "<br>";
echo "Pricing Rules Enabled: " . (get_option('pbc_enable_pricing_rules', true) ? 'YES' : 'NO') . "<br>";
echo "Base Currency: " . get_woocommerce_currency() . "<br>";

// Test 2: Test currency exchange directly
echo "<h2>2. Direct Currency Exchange Test</h2>";
try {
    $currency_exchange = new PBC_Currency_Exchange();
    
    // Test BDT to GBP conversion
    $rate = $currency_exchange->get_exchange_rate('BDT', 'GBP');
    echo "BDT to GBP Exchange Rate: " . ($rate !== false ? $rate : 'FAILED') . "<br>";
    
    if ($rate !== false) {
        $converted = $currency_exchange->convert_amount(1200, 'BDT', 'GBP');
        echo "1200 BDT = " . $converted . " GBP<br>";
    }
    
    // Test BDT to SGD conversion
    $rate_sgd = $currency_exchange->get_exchange_rate('BDT', 'SGD');
    echo "BDT to SGD Exchange Rate: " . ($rate_sgd !== false ? $rate_sgd : 'FAILED') . "<br>";
    
    if ($rate_sgd !== false) {
        $converted_sgd = $currency_exchange->convert_amount(1200, 'BDT', 'SGD');
        echo "1200 BDT = " . $converted_sgd . " SGD<br>";
    }
    
} catch (Exception $e) {
    echo "Currency Exchange Error: " . $e->getMessage() . "<br>";
}

// Test 3: Test country detection
echo "<h2>3. Country Detection Test</h2>";
try {
    $country_detector = new PBC_Country_Detector();
    $detected_country = $country_detector->detect_country();
    echo "Detected Country: " . $detected_country . "<br>";
    
    // Test country-currency mapping
    $pricing_engine = new PBC_Pricing_Engine(new PBC_Database(), $country_detector);
    $target_currency = $pricing_engine->get_country_currency($detected_country);
    echo "Target Currency for " . $detected_country . ": " . $target_currency . "<br>";
    
} catch (Exception $e) {
    echo "Country Detection Error: " . $e->getMessage() . "<br>";
}

// Test 4: Test full pricing engine
echo "<h2>4. Full Pricing Engine Test</h2>";
try {
    // Get a test product
    $products = wc_get_products(['limit' => 1]);
    if (!empty($products)) {
        $product = $products[0];
        $product_id = $product->get_id();
        $base_price = $product->get_price();
        
        echo "Test Product ID: " . $product_id . "<br>";
        echo "Base Price: " . $base_price . " " . get_woocommerce_currency() . "<br>";
        
        // Test for different countries
        $test_countries = ['GB', 'SG', 'US'];
        
        foreach ($test_countries as $country) {
            echo "<h3>Testing for Country: " . $country . "</h3>";
            
            // Force country detection
            $_SESSION['pbc_forced_country'] = $country;
            
            $price_result = $pricing_engine->get_price_adjustment($product_id, $country, $base_price);
            
            echo "Price Result for " . $country . ":<br>";
            echo "<pre>" . print_r($price_result, true) . "</pre>";
        }
        
    } else {
        echo "No products found for testing<br>";
    }
    
} catch (Exception $e) {
    echo "Pricing Engine Error: " . $e->getMessage() . "<br>";
}

// Test 5: Check error logs
echo "<h2>5. Recent Error Logs</h2>";
if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
    $log_file = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($log_file)) {
        $logs = file_get_contents($log_file);
        $recent_logs = array_slice(explode("\n", $logs), -20); // Last 20 lines
        echo "<pre>" . implode("\n", $recent_logs) . "</pre>";
    } else {
        echo "Debug log file not found<br>";
    }
} else {
    echo "WordPress debug logging is not enabled<br>";
}

echo "<h2>Debug Complete</h2>";
