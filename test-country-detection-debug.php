<?php
// Load WordPress environment
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Ensure WooCommerce is active
if (!class_exists('WooCommerce')) {
    die('WooCommerce is not active.');
}

// Include the country detector class
require_once(plugin_dir_path(__FILE__) . 'includes/class-pbc-country-detector.php');

// Instantiate the detector
$detector = new PBC_Country_Detector();

// --- Test IP Detection ---
echo "<h1>Country Detection Debug</h1>";

// 1. Get IP address as seen by WooCommerce
$ip_address = WC_Geolocation::get_ip_address();
echo "<h2>IP Detection</h2>";
echo "<p>WooCommerce detected IP: <strong>" . esc_html($ip_address) . "</strong></p>";

// 2. Geolocate the IP
$location = WC_Geolocation::geolocate_ip($ip_address);
echo "<p>Geolocation result for " . esc_html($ip_address) . ":</p>";
echo "<pre>" . print_r($location, true) . "</pre>";

// 3. Use the plugin's IP detection method
$country_from_ip = $detector->get_country_from_ip($ip_address);
echo "<p>Plugin's get_country_from_ip() result: <strong>" . esc_html($country_from_ip) . "</strong></p>";

// --- Dump SERVER variables ---
echo "<h2>SERVER Variables</h2>";
echo "<pre>";
print_r($_SERVER);
echo "</pre>";


// --- Test Overall Detection ---
echo "<h2>Overall Detection Logic</h2>";

// 1. Run the main detect_country() method
$detected_country = $detector->detect_country('auto');
echo "<p>detect_country('auto') result: <strong>" . esc_html($detected_country) . "</strong></p>";

// 2. Check for cached country
$cached_country = $detector->is_cached();
echo "<p>Is country cached for this session? <strong>" . ($cached_country ? 'Yes' : 'No') . "</strong></p>";

// 3. Display detection priority
echo "<p>Detection priority: <strong>" . implode(' > ', PBC_Country_Detector::DETECTION_PRIORITY) . "</strong></p>";

// --- Clear Cache and Re-test ---
echo "<h2>After Clearing Cache</h2>";
$detector->clear_cache();
echo "<p>Session cache cleared.</p>";

$detected_country_after_clear = $detector->detect_country('auto');
echo "<p>detect_country('auto') after cache clear: <strong>" . esc_html($detected_country_after_clear) . "</strong></p>";

?>
