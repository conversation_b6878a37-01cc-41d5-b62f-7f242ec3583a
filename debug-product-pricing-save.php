<?php
/**
 * Debug script for product pricing rule saving
 * 
 * This script helps debug why country pricing rules are not being saved
 * when products are updated in WooCommerce.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Add debug logging to the save function
add_action('woocommerce_process_product_meta', 'debug_product_pricing_save', 5, 1);

function debug_product_pricing_save($product_id) {
    error_log("=== PBC DEBUG: Product pricing save started for product ID: $product_id ===");
    
    // Log all POST data related to PBC
    $pbc_data = array();
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'pbc_') === 0) {
            $pbc_data[$key] = $value;
        }
    }
    
    error_log("PBC DEBUG: POST data: " . print_r($pbc_data, true));
    
    // Check nonce
    $nonce_check = isset($_POST['woocommerce_meta_nonce']) && 
                   wp_verify_nonce($_POST['woocommerce_meta_nonce'], 'woocommerce_save_data');
    error_log("PBC DEBUG: Nonce check result: " . ($nonce_check ? 'PASSED' : 'FAILED'));
    
    // Check user permissions
    $can_edit = current_user_can('edit_product', $product_id);
    error_log("PBC DEBUG: User can edit product: " . ($can_edit ? 'YES' : 'NO'));
    
    // Check if PBC admin class exists
    $pbc_core = PBC_Core::get_instance();
    if ($pbc_core && $pbc_core->admin) {
        error_log("PBC DEBUG: PBC Admin class is available");
        
        // Try to get existing rules
        try {
            $existing_rules = $pbc_core->database->get_pricing_rules_by_type('product', $product_id, false);
            error_log("PBC DEBUG: Existing rules count: " . count($existing_rules));
        } catch (Exception $e) {
            error_log("PBC DEBUG: Error getting existing rules: " . $e->getMessage());
        }
    } else {
        error_log("PBC DEBUG: PBC Admin class is NOT available");
    }
    
    error_log("=== PBC DEBUG: Product pricing save debug completed ===");
}

// Add debug logging to the admin save method
add_action('init', function() {
    if (class_exists('PBC_Admin')) {
        // Hook into the admin save method with higher priority
        add_action('woocommerce_process_product_meta', 'debug_admin_save_method', 1, 1);
    }
});

function debug_admin_save_method($product_id) {
    error_log("=== PBC DEBUG: Admin save method debug ===");
    
    // Check if the admin save method will be called
    $pbc_core = PBC_Core::get_instance();
    if ($pbc_core && $pbc_core->admin) {
        error_log("PBC DEBUG: PBC Admin instance exists, save method should be called");
        
        // Check if the hook is properly registered
        $hooks = $GLOBALS['wp_filter']['woocommerce_process_product_meta'] ?? null;
        if ($hooks) {
            error_log("PBC DEBUG: woocommerce_process_product_meta hooks registered: " . count($hooks->callbacks));
            foreach ($hooks->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        isset($callback['function'][0]) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'PBC_Admin') {
                        error_log("PBC DEBUG: Found PBC_Admin save method at priority: $priority");
                    }
                }
            }
        }
    } else {
        error_log("PBC DEBUG: PBC Admin instance does NOT exist");
    }
}

// Test database connection and table existence
add_action('init', function() {
    if (is_admin() && current_user_can('manage_options')) {
        error_log("=== PBC DEBUG: Database test ===");
        
        try {
            $pbc_core = PBC_Core::get_instance();
            if ($pbc_core && $pbc_core->database) {
                global $wpdb;
                $table_name = $wpdb->prefix . 'pbc_pricing_rules';
                
                // Check if table exists
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
                error_log("PBC DEBUG: Pricing rules table exists: " . ($table_exists ? 'YES' : 'NO'));
                
                if ($table_exists) {
                    // Check table structure
                    $columns = $wpdb->get_results("DESCRIBE $table_name");
                    error_log("PBC DEBUG: Table columns: " . print_r($columns, true));
                    
                    // Test a simple query
                    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
                    error_log("PBC DEBUG: Total rules in database: $count");
                }
            } else {
                error_log("PBC DEBUG: PBC Database instance not available");
            }
        } catch (Exception $e) {
            error_log("PBC DEBUG: Database test error: " . $e->getMessage());
        }
    }
});

// Add admin notice to show debug status
add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-info"><p><strong>PBC Debug:</strong> Product pricing save debugging is active. Check error logs for details.</p></div>';
    }
});

echo "Debug script loaded. Check your error logs when saving a product with pricing rules.\n";
?>