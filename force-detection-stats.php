<?php
/**
 * Force populate detection statistics for admin
 */

// Include WordPress
require_once('../../../wp-config.php');

echo "<h1>Force Detection Statistics Population</h1>";

try {
    // Initialize the core plugin
    $pbc_core = PBC_Core::get_instance();
    
    if (!$pbc_core) {
        throw new Exception('Failed to initialize PBC_Core');
    }
    
    echo "<p>✅ PBC_Core initialized</p>";
    
    // Check if country_cache table exists
    global $wpdb;
    $table_name = $wpdb->prefix . 'pbc_country_cache';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
    
    if (!$table_exists) {
        echo "<p>❌ Country cache table doesn't exist. Creating tables...</p>";
        $pbc_core->database->init();
        echo "<p>✅ Database tables created</p>";
    } else {
        echo "<p>✅ Country cache table exists</p>";
    }
    
    // Get admin IP
    $admin_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    if ($admin_ip === '::1') {
        $admin_ip = '127.0.0.1'; // Convert IPv6 localhost to IPv4
    }
    
    echo "<p><strong>Admin IP:</strong> {$admin_ip}</p>";
    
    // Create multiple detection entries for testing
    $test_entries = [
        ['session' => 'admin_session_1', 'ip' => $admin_ip, 'country' => 'US', 'method' => 'ip'],
        ['session' => 'admin_session_2', 'ip' => $admin_ip, 'country' => 'US', 'method' => 'ip'],
        ['session' => 'test_session_1', 'ip' => '*******', 'country' => 'US', 'method' => 'ip'],
        ['session' => 'test_session_2', 'ip' => '*******', 'country' => 'US', 'method' => 'ip'],
        ['session' => 'billing_test_1', 'ip' => $admin_ip, 'country' => 'US', 'method' => 'billing'],
        ['session' => 'shipping_test_1', 'ip' => $admin_ip, 'country' => 'US', 'method' => 'shipping'],
    ];
    
    echo "<h2>Inserting Test Detection Data</h2>";
    
    foreach ($test_entries as $entry) {
        $result = $pbc_core->database->save_country_cache(
            $entry['session'],
            $entry['ip'],
            $entry['country'],
            $entry['method']
        );
        
        if ($result) {
            echo "<p>✅ Inserted: {$entry['method']} detection for {$entry['ip']} → {$entry['country']}</p>";
        } else {
            echo "<p>❌ Failed to insert: {$entry['method']} detection for {$entry['ip']}</p>";
        }
    }
    
    // Check raw database counts
    echo "<h2>Raw Database Counts</h2>";
    $total_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    $ip_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE detection_method = 'ip'");
    $billing_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE detection_method = 'billing'");
    $shipping_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE detection_method = 'shipping'");
    
    echo "<p><strong>Total entries in database:</strong> {$total_count}</p>";
    echo "<p><strong>IP detections:</strong> {$ip_count}</p>";
    echo "<p><strong>Billing detections:</strong> {$billing_count}</p>";
    echo "<p><strong>Shipping detections:</strong> {$shipping_count}</p>";
    
    // Test admin statistics
    echo "<h2>Admin Statistics</h2>";
    $admin = new PBC_Admin($pbc_core->database, $pbc_core->pricing_engine);
    $stats = $admin->get_detection_statistics();
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Total Detections</td><td>{$stats['total_detections']}</td></tr>";
    echo "<tr><td>IP Detections</td><td>{$stats['ip_detections']}</td></tr>";
    echo "<tr><td>Address Detections</td><td>{$stats['address_detections']}</td></tr>";
    echo "<tr><td>IP Percentage</td><td>{$stats['ip_percentage']}%</td></tr>";
    echo "<tr><td>Address Percentage</td><td>{$stats['address_percentage']}%</td></tr>";
    echo "<tr><td>Cache Hit Rate</td><td>{$stats['cache_hit_rate']}%</td></tr>";
    echo "</table>";
    
    // Show recent entries
    echo "<h2>Recent Detection Entries</h2>";
    $recent_entries = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT 10");
    
    if ($recent_entries) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Session ID</th><th>IP</th><th>Country</th><th>Method</th><th>Created</th></tr>";
        foreach ($recent_entries as $entry) {
            echo "<tr>";
            echo "<td>" . esc_html($entry->session_id) . "</td>";
            echo "<td>" . esc_html($entry->ip_address) . "</td>";
            echo "<td>" . esc_html($entry->country_code) . "</td>";
            echo "<td>" . esc_html($entry->detection_method) . "</td>";
            echo "<td>" . esc_html($entry->created_at ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No entries found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><strong>Done!</strong> Your detection statistics should now show data.</p>";
echo "<p><a href='admin.php?page=pbc-country-detection'>View Detection Statistics</a></p>";
?>