<?php
/**
 * Test script to populate detection statistics
 */

// Include WordPress
require_once('wp-config.php');

// Check if we're in admin context
if (!is_admin() && !defined('WP_CLI')) {
    // Simulate admin context
    define('WP_ADMIN', true);
}

echo "<h1>Populating Detection Statistics</h1>";

try {
    // Initialize the core plugin
    $pbc_core = PBC_Core::get_instance();
    
    if (!$pbc_core) {
        throw new Exception('Failed to initialize PBC_Core');
    }
    
    echo "<p>✅ PBC_Core initialized successfully</p>";
    
    // Get current admin IP
    $admin_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    echo "<p><strong>Admin IP:</strong> {$admin_ip}</p>";
    
    // Create country detector with database
    $country_detector = new PBC_Country_Detector($pbc_core->database);
    echo "<p>✅ Country detector initialized with database</p>";
    
    // Test IP detection for admin
    echo "<h2>Testing IP Detection</h2>";
    $detected_country = $country_detector->get_country_from_ip($admin_ip);
    echo "<p><strong>Detected Country:</strong> " . ($detected_country ?: 'None') . "</p>";
    
    // Force some detection events to populate statistics
    echo "<h2>Populating Test Data</h2>";
    
    // Simulate multiple detection events
    $test_data = [
        ['ip' => $admin_ip, 'country' => $detected_country ?: 'US'],
        ['ip' => '*******', 'country' => 'US'],
        ['ip' => '*******', 'country' => 'US'],
        ['ip' => '**************', 'country' => 'US'],
    ];
    
    foreach ($test_data as $data) {
        // Manually save detection data to database
        $session_id = 'test_session_' . uniqid();
        $result = $pbc_core->database->save_country_cache(
            $session_id,
            $data['ip'],
            $data['country'],
            'ip'
        );
        
        if ($result) {
            echo "<p>✅ Saved detection: IP {$data['ip']} → {$data['country']}</p>";
        } else {
            echo "<p>❌ Failed to save detection for IP {$data['ip']}</p>";
        }
    }
    
    // Test the admin statistics method
    echo "<h2>Current Statistics</h2>";
    $admin = new PBC_Admin($pbc_core->database, $pbc_core->pricing_engine);
    $stats = $admin->get_detection_statistics();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Total Detections</td><td>{$stats['total_detections']}</td></tr>";
    echo "<tr><td>IP Detections</td><td>{$stats['ip_detections']}</td></tr>";
    echo "<tr><td>Address Detections</td><td>{$stats['address_detections']}</td></tr>";
    echo "<tr><td>IP Percentage</td><td>{$stats['ip_percentage']}%</td></tr>";
    echo "<tr><td>Address Percentage</td><td>{$stats['address_percentage']}%</td></tr>";
    echo "<tr><td>Cache Hit Rate</td><td>{$stats['cache_hit_rate']}%</td></tr>";
    echo "</table>";
    
    // Test detection with current session
    echo "<h2>Testing Current Session Detection</h2>";
    session_start();
    $current_country = $country_detector->detect_country('auto');
    echo "<p><strong>Current Session Country:</strong> {$current_country}</p>";
    
    // Get updated statistics
    $updated_stats = $admin->get_detection_statistics();
    echo "<h3>Updated Statistics:</h3>";
    echo "<p><strong>Total Detections:</strong> {$updated_stats['total_detections']}</p>";
    echo "<p><strong>IP Detections:</strong> {$updated_stats['ip_detections']}</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><strong>Test completed!</strong> Check your admin detection statistics page now.</p>";
?>