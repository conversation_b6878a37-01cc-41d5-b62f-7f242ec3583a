<?php
/**
 * Currency exchange rate service for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Currency Exchange Class
 */
class PBC_Currency_Exchange {

    /**
     * Cache key prefix for exchange rates
     */
    const CACHE_PREFIX = 'pbc_exchange_';

    /**
     * Cache expiration time (24 hours)
     */
    const CACHE_EXPIRATION = 86400;

    /**
     * API timeout in seconds
     */
    const API_TIMEOUT = 10;

    /**
     * Supported exchange rate providers
     */
    const PROVIDERS = [
        'exchangerate_api' => 'ExchangeRate-API',
        'ecb' => 'European Central Bank',
        'bank_of_canada' => 'Bank of Canada'
    ];

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Constructor
     */
    public function __construct() {
        $this->logger = PBC_Logger::get_instance();
        $this->error_handler = PBC_Error_Handler::get_instance();
    }

    /**
     * Get exchange rate between two currencies
     *
     * @param string $from_currency Source currency code
     * @param string $to_currency Target currency code
     * @return float|false Exchange rate or false on error
     */
    public function get_exchange_rate($from_currency, $to_currency) {
        try {
            // Validate currency codes
            if (!$this->is_valid_currency_code($from_currency) || !$this->is_valid_currency_code($to_currency)) {
                throw new InvalidArgumentException('Invalid currency codes provided');
            }

            // Same currency = 1.0 rate
            if ($from_currency === $to_currency) {
                return 1.0;
            }

            // Check cache first
            $cached_rate = $this->get_cached_rate($from_currency, $to_currency);
            if ($cached_rate !== false) {
                return $cached_rate;
            }

            // Try to fetch from configured providers
            $providers = $this->get_enabled_providers();
            $rate = false;

            foreach ($providers as $provider) {
                $rate = $this->fetch_rate_from_provider($provider, $from_currency, $to_currency);
                if ($rate !== false) {
                    break;
                }
            }

            if ($rate === false) {
                // Try fallback rates for common conversions
                $rate = $this->get_fallback_rate($from_currency, $to_currency);

                if ($rate === false) {
                    throw new Exception("Unable to fetch exchange rate for {$from_currency} to {$to_currency}");
                }

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("PBC Currency: Using fallback rate {$from_currency} to {$to_currency} = {$rate}");
                }
            }

            // Cache the rate
            $this->cache_exchange_rate($from_currency, $to_currency, $rate);

            // Log successful rate fetch
            $this->logger->log_exchange_rate_fetch($from_currency, $to_currency, $rate);

            return $rate;

        } catch (Exception $e) {
            return $this->error_handler->handle_currency_error($e, [
                'from_currency' => $from_currency,
                'to_currency' => $to_currency,
                'function' => __FUNCTION__
            ]);
        }
    }

    /**
     * Convert amount from one currency to another
     *
     * @param float $amount Amount to convert
     * @param string $from_currency Source currency code
     * @param string $to_currency Target currency code
     * @return float|false Converted amount or false on error
     */
    public function convert_amount($amount, $from_currency, $to_currency) {
        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC Currency Convert: {$amount} {$from_currency} to {$to_currency}");
        }

        $rate = $this->get_exchange_rate($from_currency, $to_currency);

        if ($rate === false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC Currency Convert Failed: No exchange rate for {$from_currency} to {$to_currency}");
            }
            return false;
        }

        $converted_amount = floatval($amount) * $rate;

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC Currency Convert Success: {$amount} {$from_currency} = {$converted_amount} {$to_currency} (rate: {$rate})");
        }

        return $converted_amount;
    }

    /**
     * Fetch exchange rate from ExchangeRate-API
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Exchange rate or false on error
     */
    private function fetch_from_exchangerate_api($from_currency, $to_currency) {
        $api_key = get_option('pbc_exchangerate_api_key', '');

        if (empty($api_key)) {
            // Use free tier without API key
            $url = "https://api.exchangerate-api.com/v4/latest/{$from_currency}";
        } else {
            // Use paid tier with API key
            $url = "https://v6.exchangerate-api.com/v6/{$api_key}/latest/{$from_currency}";
        }

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC ExchangeRate API: Fetching {$from_currency} to {$to_currency} from {$url}");
        }

        $response = wp_remote_get($url, [
            'timeout' => self::API_TIMEOUT,
            'headers' => [
                'User-Agent' => 'PriceByCountry-WordPress-Plugin'
            ]
        ]);

        if (is_wp_error($response)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate API Error: " . $response->get_error_message());
            }
            return false;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate API HTTP Error: {$response_code}");
            }
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate API: Invalid JSON response");
            }
            return false;
        }

        // Check for API errors
        if (isset($data['error-type'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate API Error: " . $data['error-type']);
            }
            return false;
        }

        if (!isset($data['rates'][$to_currency])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate API: {$to_currency} not found in rates. Available: " . implode(', ', array_keys($data['rates'] ?? [])));
            }
            return false;
        }

        $rate = floatval($data['rates'][$to_currency]);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC ExchangeRate API Success: {$from_currency} to {$to_currency} = {$rate}");
        }

        return $rate;
    }

    /**
     * Fetch exchange rate from European Central Bank
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Exchange rate or false on error
     */
    private function fetch_from_ecb($from_currency, $to_currency) {
        // ECB only provides EUR-based rates
        if ($from_currency !== 'EUR' && $to_currency !== 'EUR') {
            // Need to convert through EUR
            $eur_from_rate = $this->fetch_from_ecb('EUR', $from_currency);
            $eur_to_rate = $this->fetch_from_ecb('EUR', $to_currency);
            
            if ($eur_from_rate === false || $eur_to_rate === false) {
                return false;
            }
            
            return $eur_to_rate / $eur_from_rate;
        }

        $url = 'https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml';
        
        $response = wp_remote_get($url, [
            'timeout' => self::API_TIMEOUT
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        
        // Parse XML
        $xml = simplexml_load_string($body);
        if (!$xml) {
            return false;
        }

        // Navigate XML structure
        $rates = $xml->Cube->Cube->Cube;
        
        if ($from_currency === 'EUR') {
            // EUR to other currency
            foreach ($rates as $rate) {
                if ((string)$rate['currency'] === $to_currency) {
                    return floatval($rate['rate']);
                }
            }
        } else {
            // Other currency to EUR
            foreach ($rates as $rate) {
                if ((string)$rate['currency'] === $from_currency) {
                    return 1.0 / floatval($rate['rate']);
                }
            }
        }

        return false;
    }

    /**
     * Fetch exchange rate from Bank of Canada
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Exchange rate or false on error
     */
    private function fetch_from_bank_of_canada($from_currency, $to_currency) {
        // Bank of Canada only provides CAD-based rates
        if ($from_currency !== 'CAD' && $to_currency !== 'CAD') {
            // Need to convert through CAD
            $cad_from_rate = $this->fetch_from_bank_of_canada('CAD', $from_currency);
            $cad_to_rate = $this->fetch_from_bank_of_canada('CAD', $to_currency);
            
            if ($cad_from_rate === false || $cad_to_rate === false) {
                return false;
            }
            
            return $cad_to_rate / $cad_from_rate;
        }

        $url = 'https://www.bankofcanada.ca/valet/observations/group/FX_RATES_DAILY/json?recent=1';
        
        $response = wp_remote_get($url, [
            'timeout' => self::API_TIMEOUT
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['observations'][0])) {
            return false;
        }

        $rates = $data['observations'][0];
        
        if ($from_currency === 'CAD') {
            // CAD to other currency
            $rate_key = 'FX' . $to_currency . 'CAD';
            if (isset($rates[$rate_key]['v'])) {
                return 1.0 / floatval($rates[$rate_key]['v']);
            }
        } else {
            // Other currency to CAD
            $rate_key = 'FX' . $from_currency . 'CAD';
            if (isset($rates[$rate_key]['v'])) {
                return floatval($rates[$rate_key]['v']);
            }
        }

        return false;
    }    /**

     * Fetch rate from specific provider
     *
     * @param string $provider Provider name
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Exchange rate or false on error
     */
    private function fetch_rate_from_provider($provider, $from_currency, $to_currency) {
        switch ($provider) {
            case 'exchangerate_api':
                return $this->fetch_from_exchangerate_api($from_currency, $to_currency);

            case 'ecb':
                return $this->fetch_from_ecb($from_currency, $to_currency);

            case 'bank_of_canada':
                return $this->fetch_from_bank_of_canada($from_currency, $to_currency);

            case 'fixer_io':
                return $this->fetch_from_fixer_io($from_currency, $to_currency);

            default:
                return false;
        }
    }

    /**
     * Fetch exchange rate from Fixer.io (good for BDT support)
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Exchange rate or false on error
     */
    private function fetch_from_fixer_io($from_currency, $to_currency) {
        // Use alternative free API that supports BDT
        $url = "https://api.exchangerate.host/latest?base={$from_currency}&symbols={$to_currency}";

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC ExchangeRate.host API: Fetching {$from_currency} to {$to_currency}");
        }

        $response = wp_remote_get($url, [
            'timeout' => self::API_TIMEOUT,
            'headers' => [
                'User-Agent' => 'PriceByCountry-WordPress-Plugin'
            ]
        ]);

        if (is_wp_error($response)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate.host API Error: " . $response->get_error_message());
            }
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !$data['success'] || !isset($data['rates'][$to_currency])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC ExchangeRate.host API: Invalid response or {$to_currency} not found");
                if (isset($data['error'])) {
                    error_log("PBC ExchangeRate.host API Error Details: " . print_r($data['error'], true));
                }
            }
            return false;
        }

        $rate = floatval($data['rates'][$to_currency]);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC ExchangeRate.host API Success: {$from_currency} to {$to_currency} = {$rate}");
        }

        return $rate;
    }

    /**
     * Get fallback exchange rate for common currency pairs
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Fallback rate or false if not available
     */
    private function get_fallback_rate($from_currency, $to_currency) {
        // Approximate rates (updated periodically) - these should be updated regularly
        $fallback_rates = [
            'BDT_GBP' => 0.0067,  // 1 BDT = 0.0067 GBP (approximate)
            'BDT_USD' => 0.0083,  // 1 BDT = 0.0083 USD (approximate)
            'BDT_EUR' => 0.0076,  // 1 BDT = 0.0076 EUR (approximate)
            'USD_GBP' => 0.79,    // 1 USD = 0.79 GBP (approximate)
            'USD_EUR' => 0.92,    // 1 USD = 0.92 EUR (approximate)
            'EUR_GBP' => 0.86,    // 1 EUR = 0.86 GBP (approximate)
            'GBP_USD' => 1.27,    // 1 GBP = 1.27 USD (approximate)
            'GBP_EUR' => 1.16,    // 1 GBP = 1.16 EUR (approximate)
            'EUR_USD' => 1.09,    // 1 EUR = 1.09 USD (approximate)
        ];

        $rate_key = $from_currency . '_' . $to_currency;

        if (isset($fallback_rates[$rate_key])) {
            return $fallback_rates[$rate_key];
        }

        // Try reverse rate
        $reverse_key = $to_currency . '_' . $from_currency;
        if (isset($fallback_rates[$reverse_key])) {
            return 1.0 / $fallback_rates[$reverse_key];
        }

        return false;
    }

    /**
     * Get enabled exchange rate providers from settings
     *
     * @return array Enabled providers in priority order
     */
    private function get_enabled_providers() {
        $default_providers = ['exchangerate_api', 'fixer_io', 'ecb'];
        return get_option('pbc_exchange_providers', $default_providers);
    }

    /**
     * Validate currency code
     *
     * @param string $currency_code Currency code to validate
     * @return bool True if valid, false otherwise
     */
    private function is_valid_currency_code($currency_code) {
        // Basic validation - 3 letter uppercase code
        return is_string($currency_code) && 
               strlen($currency_code) === 3 && 
               ctype_upper($currency_code) &&
               ctype_alpha($currency_code);
    }

    /**
     * Get cached exchange rate
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @return float|false Cached rate or false if not cached
     */
    private function get_cached_rate($from_currency, $to_currency) {
        $cache_key = self::CACHE_PREFIX . $from_currency . '_' . $to_currency;
        return get_transient($cache_key);
    }

    /**
     * Cache exchange rate
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @param float $rate Exchange rate
     */
    private function cache_exchange_rate($from_currency, $to_currency, $rate) {
        $cache_key = self::CACHE_PREFIX . $from_currency . '_' . $to_currency;
        set_transient($cache_key, $rate, self::CACHE_EXPIRATION);
        
        // Also cache the reverse rate
        $reverse_cache_key = self::CACHE_PREFIX . $to_currency . '_' . $from_currency;
        $reverse_rate = 1.0 / $rate;
        set_transient($reverse_cache_key, $reverse_rate, self::CACHE_EXPIRATION);
    }

    /**
     * Clear exchange rate cache
     *
     * @param string $from_currency Source currency (optional)
     * @param string $to_currency Target currency (optional)
     */
    public function clear_exchange_cache($from_currency = null, $to_currency = null) {
        global $wpdb;
        
        if ($from_currency && $to_currency) {
            // Clear specific rate
            $cache_key = self::CACHE_PREFIX . $from_currency . '_' . $to_currency;
            delete_transient($cache_key);
            
            // Clear reverse rate
            $reverse_cache_key = self::CACHE_PREFIX . $to_currency . '_' . $from_currency;
            delete_transient($reverse_cache_key);
        } else {
            // Clear all exchange rate cache
            $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                    '_transient_' . self::CACHE_PREFIX . '%'
                )
            );
        }
    }

    /**
     * Get multiple exchange rates for batch processing
     *
     * @param array $currency_pairs Array of [from, to] currency pairs
     * @return array Exchange rates keyed by 'FROM_TO'
     */
    public function get_batch_exchange_rates($currency_pairs) {
        $rates = [];
        
        foreach ($currency_pairs as $pair) {
            if (!is_array($pair) || count($pair) !== 2) {
                continue;
            }
            
            list($from, $to) = $pair;
            $key = $from . '_' . $to;
            $rates[$key] = $this->get_exchange_rate($from, $to);
        }
        
        return $rates;
    }

    /**
     * Get supported currencies for a provider
     *
     * @param string $provider Provider name
     * @return array|false Supported currencies or false on error
     */
    public function get_supported_currencies($provider = 'exchangerate_api') {
        switch ($provider) {
            case 'exchangerate_api':
                return $this->get_exchangerate_api_currencies();
            
            case 'ecb':
                return $this->get_ecb_currencies();
            
            case 'bank_of_canada':
                return $this->get_bank_of_canada_currencies();
            
            default:
                return false;
        }
    }

    /**
     * Get currencies supported by ExchangeRate-API
     *
     * @return array Supported currencies
     */
    private function get_exchangerate_api_currencies() {
        // Cache supported currencies for 7 days
        $cache_key = 'pbc_exchangerate_api_currencies';
        $cached = get_transient($cache_key);
        
        if ($cached !== false) {
            return $cached;
        }

        $url = 'https://api.exchangerate-api.com/v4/latest/USD';
        $response = wp_remote_get($url, ['timeout' => self::API_TIMEOUT]);
        
        if (is_wp_error($response)) {
            return $this->get_fallback_currencies();
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['rates'])) {
            return $this->get_fallback_currencies();
        }

        $currencies = array_keys($data['rates']);
        $currencies[] = 'USD'; // Add base currency
        sort($currencies);
        
        set_transient($cache_key, $currencies, 7 * DAY_IN_SECONDS);
        return $currencies;
    }

    /**
     * Get currencies supported by ECB
     *
     * @return array Supported currencies
     */
    private function get_ecb_currencies() {
        return [
            'EUR', 'USD', 'JPY', 'BGN', 'CZK', 'DKK', 'GBP', 'HUF', 'PLN', 'RON', 'SEK', 'CHF', 'ISK', 'NOK', 'HRK', 'RUB', 'TRY', 'AUD', 'BRL', 'CAD', 'CNY', 'HKD', 'IDR', 'ILS', 'INR', 'KRW', 'MXN', 'MYR', 'NZD', 'PHP', 'SGD', 'THB', 'ZAR'
        ];
    }

    /**
     * Get currencies supported by Bank of Canada
     *
     * @return array Supported currencies
     */
    private function get_bank_of_canada_currencies() {
        return [
            'CAD', 'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CHF', 'CNY', 'INR', 'MXN', 'NOK', 'NZD', 'SEK', 'BRL', 'DKK', 'HKD', 'KRW', 'PLN', 'RUB', 'SGD', 'ZAR'
        ];
    }

    /**
     * Get fallback currency list
     *
     * @return array Common currencies
     */
    private function get_fallback_currencies() {
        return [
            'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD', 'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW'
        ];
    }

    /**
     * Test exchange rate provider connectivity
     *
     * @param string $provider Provider to test
     * @return array Test result
     */
    public function test_provider($provider) {
        $start_time = microtime(true);
        
        try {
            $rate = $this->fetch_rate_from_provider($provider, 'USD', 'EUR');
            $end_time = microtime(true);
            $response_time = round(($end_time - $start_time) * 1000, 2);
            
            if ($rate === false) {
                return [
                    'success' => false,
                    'error' => 'Failed to fetch exchange rate',
                    'response_time' => $response_time
                ];
            }
            
            return [
                'success' => true,
                'rate' => $rate,
                'response_time' => $response_time,
                'provider' => self::PROVIDERS[$provider] ?? $provider
            ];
            
        } catch (Exception $e) {
            $end_time = microtime(true);
            $response_time = round(($end_time - $start_time) * 1000, 2);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'response_time' => $response_time
            ];
        }
    }

    /**
     * Get exchange rate statistics for admin dashboard
     *
     * @return array Exchange rate statistics
     */
    public function get_exchange_stats() {
        global $wpdb;
        
        $cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::CACHE_PREFIX . '%'
            )
        );
        
        $providers = $this->get_enabled_providers();
        $provider_status = [];
        
        foreach ($providers as $provider) {
            $test_result = $this->test_provider($provider);
            $provider_status[$provider] = [
                'name' => self::PROVIDERS[$provider] ?? $provider,
                'status' => $test_result['success'] ? 'online' : 'offline',
                'response_time' => $test_result['response_time'] ?? 0,
                'error' => $test_result['error'] ?? null
            ];
        }
        
        return [
            'cached_rates' => intval($cache_count),
            'enabled_providers' => count($providers),
            'provider_status' => $provider_status,
            'cache_expiration' => self::CACHE_EXPIRATION / 3600 . ' hours'
        ];
    }

    /**
     * Warm up exchange rate cache for common currency pairs
     *
     * @param array $base_currencies Base currencies to warm up
     * @param array $target_currencies Target currencies to warm up
     * @return int Number of rates cached
     */
    public function warm_exchange_cache($base_currencies, $target_currencies) {
        $cached_count = 0;
        
        foreach ($base_currencies as $base) {
            foreach ($target_currencies as $target) {
                if ($base === $target) {
                    continue;
                }
                
                // Skip if already cached
                if ($this->get_cached_rate($base, $target) !== false) {
                    continue;
                }
                
                $rate = $this->get_exchange_rate($base, $target);
                if ($rate !== false) {
                    $cached_count++;
                }
            }
        }
        
        return $cached_count;
    }

    /**
     * Get historical exchange rate (if supported by provider)
     *
     * @param string $from_currency Source currency
     * @param string $to_currency Target currency
     * @param string $date Date in Y-m-d format
     * @return float|false Historical rate or false if not available
     */
    public function get_historical_rate($from_currency, $to_currency, $date) {
        // Currently only implement for ExchangeRate-API with paid plan
        $api_key = get_option('pbc_exchangerate_api_key', '');
        
        if (empty($api_key)) {
            return false; // Historical rates require paid plan
        }
        
        $url = "https://v6.exchangerate-api.com/v6/{$api_key}/history/{$from_currency}/{$date}";
        
        $response = wp_remote_get($url, [
            'timeout' => self::API_TIMEOUT
        ]);
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['conversion_rates'][$to_currency])) {
            return false;
        }
        
        return floatval($data['conversion_rates'][$to_currency]);
    }
}