<?php

// Load WordPress environment
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Define a dummy product ID for testing
define('TEST_PRODUCT_ID', 123);

// Mock the wc_get_product function
if (!function_exists('wc_get_product')) {
    function wc_get_product($id) {
        $product = new WC_Product_Simple();
        $product->set_id($id);
        $product->set_name('Test Product');
        $product->set_regular_price(100);
        return $product;
    }
}

// Instantiate necessary classes
$country_detector = new PBC_Country_Detector(null);
$pricing_engine = new PBC_Pricing_Engine(new PBC_Database(), $country_detector);

// --- Test Case 1: US IP Address ---
define('PBC_FORCE_IP', '*******'); // Google's DNS, located in the US

echo "--- Running Test Case 1: US IP Address (*******) ---<br>";

// Detect country
$detected_country = $country_detector->detect_country();
echo "Detected Country: " . $detected_country . "<br>";

// Get price adjustment
$price_adjustment = $pricing_engine->get_price_adjustment(TEST_PRODUCT_ID, $detected_country);

echo "Price Adjustment Details:<pre>";
print_r($price_adjustment);
echo "</pre>";

// --- Test Case 2: Bangladeshi IP Address ---
define('PBC_FORCE_IP_2', '***************'); // A sample Bangladeshi IP

echo "<br>--- Running Test Case 2: Bangladeshi IP Address (***************) ---<br>";

// Detect country
$detected_country_bd = $country_detector->detect_country();
echo "Detected Country: " . $detected_country_bd . "<br>";

// Get price adjustment
$price_adjustment_bd = $pricing_engine->get_price_adjustment(TEST_PRODUCT_ID, $detected_country_bd);

echo "Price Adjustment Details:<pre>";
print_r($price_adjustment_bd);
echo "</pre>";

?>