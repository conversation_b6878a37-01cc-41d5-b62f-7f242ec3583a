<?php
/**
 * Fix script for currency conversion issues
 * 
 * This script will:
 * 1. Enable currency conversion
 * 2. Clear all caches
 * 3. Test the conversion
 */

// WordPress bootstrap
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

echo "<h1>Price by Country - Currency Conversion Fix</h1>\n";
echo "<pre>\n";

// Check if plugin is active
if (!class_exists('PBC_Core')) {
    echo "ERROR: Price by Country plugin is not active!\n";
    exit;
}

echo "=== APPLYING CURRENCY CONVERSION FIX ===\n";

// 1. Enable currency conversion
update_option('pbc_enable_currency_conversion', 1);
echo "✓ Currency conversion enabled\n";

// 2. Enable pricing rules (needed for the conversion to work)
update_option('pbc_enable_pricing_rules', 1);
echo "✓ Pricing rules enabled\n";

// 3. Set better exchange providers
update_option('pbc_exchange_providers', ['exchangerate_api', 'fixer_io', 'ecb']);
echo "✓ Exchange providers updated\n";

// 4. Clear all caches
try {
    // Clear WordPress transients
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_pbc_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_pbc_%'");
    echo "✓ WordPress transients cleared\n";
    
    // Clear WooCommerce caches
    if (function_exists('wc_delete_product_transients')) {
        $products = get_posts(['post_type' => 'product', 'posts_per_page' => -1, 'fields' => 'ids']);
        foreach ($products as $product_id) {
            wc_delete_product_transients($product_id);
        }
        echo "✓ WooCommerce product caches cleared\n";
    }
    
    // Clear object cache if available
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
        echo "✓ Object cache cleared\n";
    }
    
} catch (Exception $e) {
    echo "⚠ Cache clearing error: " . $e->getMessage() . "\n";
}

// 5. Test currency conversion
echo "\n=== TESTING CURRENCY CONVERSION ===\n";
try {
    $currency_exchange = new PBC_Currency_Exchange();
    
    // Test BDT to GBP conversion
    echo "Testing BDT to GBP conversion...\n";
    $rate = $currency_exchange->get_exchange_rate('BDT', 'GBP');
    
    if ($rate !== false) {
        echo "✓ BDT to GBP rate: {$rate}\n";
        
        // Test converting 1200 BDT (your product price)
        $converted = $currency_exchange->convert_amount(1200, 'BDT', 'GBP');
        echo "✓ 1200 BDT = " . number_format($converted, 2) . " GBP\n";
        
    } else {
        echo "✗ Failed to get BDT to GBP rate\n";
    }
    
} catch (Exception $e) {
    echo "✗ Currency conversion test failed: " . $e->getMessage() . "\n";
}

// 6. Test with a real product
echo "\n=== TESTING WITH REAL PRODUCT ===\n";
try {
    $products = get_posts([
        'post_type' => 'product',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ]);
    
    if (!empty($products)) {
        $product_id = $products[0]->ID;
        $product = wc_get_product($product_id);
        
        echo "Testing with Product: " . $product->get_name() . "\n";
        echo "Original Price: " . $product->get_price() . " " . get_woocommerce_currency() . "\n";
        
        // Test price adjustment for GB
        $pricing_engine = new PBC_Pricing_Engine();
        $price_result = $pricing_engine->get_price_adjustment($product_id, 'GB', floatval($product->get_price()));
        
        echo "Price for GB visitors:\n";
        echo "- Adjusted Price: " . $price_result['adjusted_price'] . "\n";
        echo "- Currency: " . ($price_result['currency_code'] ?? get_woocommerce_currency()) . "\n";
        echo "- Converted: " . (isset($price_result['currency_converted']) && $price_result['currency_converted'] ? 'YES' : 'NO') . "\n";
        
        if (isset($price_result['currency_converted']) && $price_result['currency_converted']) {
            echo "✓ Currency conversion is working!\n";
        } else {
            echo "⚠ Currency conversion may not be working properly\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Product testing failed: " . $e->getMessage() . "\n";
}

echo "\n=== FIX COMPLETE ===\n";
echo "Next steps:\n";
echo "1. Visit your product page and check if prices convert for UK visitors\n";
echo "2. Enable WordPress debug logging to see detailed conversion logs\n";
echo "3. Consider getting an ExchangeRate-API key for better reliability\n";
echo "4. Test with different countries to ensure conversion works\n";

echo "</pre>\n";
?>
