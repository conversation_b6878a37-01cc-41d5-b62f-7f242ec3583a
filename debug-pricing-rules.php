<?php

/**
 * Debugging script to check pricing rules for a specific country.
 */

// Load WordPress environment
if (!defined('ABSPATH')) {
    require_once(dirname(__FILE__) . '/../../../wp-load.php');
}

// Check if the user has the appropriate permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Ensure the database class is loaded
if (!class_exists('PBC_Database')) {
    require_once(dirname(__FILE__) . '/includes/class-pbc-database.php');
}

// Instantiate the database class
$pbc_database = new PBC_Database();

// The country to debug
$country_code = 'SG';

// Get the pricing rules for the specified country
$rules = $pbc_database->get_pricing_rules_by_country($country_code);

// Display the results
header('Content-Type: text/plain');

echo "--- Debugging Pricing Rules for Country: {$country_code} ---\\n\\n";

if (empty($rules) || (empty($rules['global']) && empty($rules['category']) && empty($rules['product']))) {
    echo "No active pricing rules found for {$country_code}.\n";
} else {
    echo "Found the following rules:\\n\\n";
    print_r($rules);
}

echo "\\n--- End of Debug ---\\n";

