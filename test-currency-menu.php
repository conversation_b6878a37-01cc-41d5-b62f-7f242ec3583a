<?php
/**
 * Test file to check if currency settings menu is working
 * 
 * Add this to your WordPress admin to test: /wp-admin/admin.php?page=test-currency-menu
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add test menu item
add_action('admin_menu', function() {
    add_submenu_page(
        'woocommerce',
        'Test Currency Menu',
        'Test Currency Menu',
        'manage_woocommerce',
        'test-currency-menu',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Currency Settings Test</h1>';
            
            // Check if currency exchange class exists
            if (class_exists('PBC_Currency_Exchange')) {
                echo '<p style="color: green;">✓ PBC_Currency_Exchange class loaded successfully</p>';
                
                $exchange = new PBC_Currency_Exchange();
                $rate = $exchange->get_exchange_rate('USD', 'EUR');
                
                if ($rate !== false) {
                    echo '<p style="color: green;">✓ Exchange rate fetch successful: 1 USD = ' . $rate . ' EUR</p>';
                } else {
                    echo '<p style="color: red;">✗ Exchange rate fetch failed</p>';
                }
            } else {
                echo '<p style="color: red;">✗ PBC_Currency_Exchange class not found</p>';
            }
            
            // Check if currency settings class exists
            if (class_exists('PBC_Currency_Settings')) {
                echo '<p style="color: green;">✓ PBC_Currency_Settings class loaded successfully</p>';
            } else {
                echo '<p style="color: red;">✗ PBC_Currency_Settings class not found</p>';
            }
            
            // Check if core class has currency settings
            if (class_exists('PBC_Core')) {
                $core = PBC_Core::get_instance();
                if (property_exists($core, 'currency_settings') && $core->currency_settings) {
                    echo '<p style="color: green;">✓ Currency settings initialized in core</p>';
                } else {
                    echo '<p style="color: red;">✗ Currency settings not initialized in core</p>';
                }
            }
            
            echo '</div>';
        }
    );
}, 25);