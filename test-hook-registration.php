<?php
/**
 * Test if the PBC admin hook is properly registered
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h2>Testing PBC Hook Registration</h2>\n";

// Check if we're in admin context
if (!is_admin()) {
    echo "<p style='color: orange;'>⚠ Not in admin context, switching...</p>\n";
    define('WP_ADMIN', true);
}

// Check if PBC is loaded
if (!class_exists('PBC_Core')) {
    echo "<p style='color: red;'>ERROR: PBC_Core class not found.</p>\n";
    exit;
}

$pbc_core = PBC_Core::get_instance();
if (!$pbc_core || !$pbc_core->admin) {
    echo "<p style='color: red;'>ERROR: PBC Admin not available.</p>\n";
    exit;
}

echo "<p style='color: green;'>✓ PBC Admin available</p>\n";

// Check hook registration
global $wp_filter;
$hook_name = 'woocommerce_process_product_meta';

echo "<h3>Checking $hook_name Hook</h3>\n";

if (isset($wp_filter[$hook_name])) {
    $hooks = $wp_filter[$hook_name];
    echo "<p style='color: green;'>✓ $hook_name hook is registered with " . count($hooks->callbacks) . " priority levels</p>\n";
    
    $found_pbc_hook = false;
    foreach ($hooks->callbacks as $priority => $callbacks) {
        echo "<p>Priority $priority has " . count($callbacks) . " callbacks:</p>\n";
        foreach ($callbacks as $id => $callback) {
            if (is_array($callback['function'])) {
                if (isset($callback['function'][0]) && is_object($callback['function'][0])) {
                    $class_name = get_class($callback['function'][0]);
                    $method_name = $callback['function'][1] ?? 'unknown';
                    echo "<p>  - $class_name::$method_name</p>\n";
                    
                    if ($class_name === 'PBC_Admin') {
                        echo "<p style='color: green;'>    ✓ Found PBC_Admin hook!</p>\n";
                        $found_pbc_hook = true;
                    }
                } else {
                    echo "<p>  - " . print_r($callback['function'], true) . "</p>\n";
                }
            } else {
                echo "<p>  - " . $callback['function'] . "</p>\n";
            }
        }
    }
    
    if (!$found_pbc_hook) {
        echo "<p style='color: red;'>ERROR: PBC_Admin hook not found!</p>\n";
    }
} else {
    echo "<p style='color: red;'>ERROR: $hook_name hook is not registered at all!</p>\n";
}

// Test if the method exists and is callable
echo "<h3>Testing Method Availability</h3>\n";

if (method_exists($pbc_core->admin, 'save_product_pricing_rules')) {
    echo "<p style='color: green;'>✓ save_product_pricing_rules method exists</p>\n";
    
    if (is_callable([$pbc_core->admin, 'save_product_pricing_rules'])) {
        echo "<p style='color: green;'>✓ save_product_pricing_rules method is callable</p>\n";
    } else {
        echo "<p style='color: red;'>ERROR: save_product_pricing_rules method is not callable</p>\n";
    }
} else {
    echo "<p style='color: red;'>ERROR: save_product_pricing_rules method does not exist</p>\n";
}

// Test manual hook addition (in case it's not being added automatically)
echo "<h3>Testing Manual Hook Addition</h3>\n";

// Remove existing hook first
remove_action('woocommerce_process_product_meta', [$pbc_core->admin, 'save_product_pricing_rules']);

// Add it manually
add_action('woocommerce_process_product_meta', [$pbc_core->admin, 'save_product_pricing_rules'], 10, 1);

echo "<p style='color: green;'>✓ Manually added hook</p>\n";

// Check if it's now registered
if (isset($wp_filter[$hook_name])) {
    $hooks = $wp_filter[$hook_name];
    $found_pbc_hook = false;
    foreach ($hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && 
                isset($callback['function'][0]) && 
                is_object($callback['function'][0]) && 
                get_class($callback['function'][0]) === 'PBC_Admin') {
                $found_pbc_hook = true;
                break 2;
            }
        }
    }
    
    if ($found_pbc_hook) {
        echo "<p style='color: green;'>✓ Hook is now properly registered</p>\n";
    } else {
        echo "<p style='color: red;'>ERROR: Hook still not found after manual addition</p>\n";
    }
}

echo "<h3>Test Complete</h3>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li>Try saving a product with pricing rules now</li>\n";
echo "<li>Check your error logs for PBC debug messages</li>\n";
echo "<li>If you still don't see debug messages, the issue is with hook registration</li>\n";
echo "</ul>\n";
?>