<?php
/**
 * Test script to trigger country detection and populate statistics
 */

// Include WordPress
require_once('wp-config.php');

// Check if we're in admin and user has permissions
if (!is_admin() || !current_user_can('manage_woocommerce')) {
    wp_die('Access denied');
}

// Start session if not already started
if (!session_id()) {
    session_start();
}

echo "<h1>Country Detection Test & Statistics Population</h1>";

// Initialize the core plugin
$pbc_core = PBC_Core::get_instance();
$country_detector = new PBC_Country_Detector($pbc_core->database);

echo "<h2>Current Detection Test</h2>";

// Test current user's country detection
$detected_country = $country_detector->detect_country('auto');
echo "<p><strong>Detected Country:</strong> " . $detected_country . "</p>";

// Get current IP
$current_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
echo "<p><strong>Current IP:</strong> " . $current_ip . "</p>";

// Test IP detection specifically
$ip_country = $country_detector->get_country_from_ip($current_ip);
echo "<p><strong>IP-based Detection:</strong> " . ($ip_country ?: 'Failed') . "</p>";

echo "<h2>Populating Test Data</h2>";

// Create some test detection entries to populate statistics
$test_ips = [
    '*******' => 'US',      // Google DNS
    '*******' => 'US',      // Cloudflare
    '**************' => 'US', // OpenDNS
    '*********' => 'RU',    // Yandex
    '***************' => 'CN' // China DNS
];

$database = $pbc_core->database;
$session_base = 'test_session_';

foreach ($test_ips as $ip => $expected_country) {
    $session_id = $session_base . md5($ip);
    
    // Save test detection data
    $result = $database->save_country_cache(
        $session_id,
        $ip,
        $expected_country,
        'ip'
    );
    
    echo "<p>Added test data for IP {$ip} -> {$expected_country}: " . ($result ? 'Success' : 'Failed') . "</p>";
}

// Add some billing/shipping detection test data
$test_user_data = [
    ['session' => 'billing_test_1', 'ip' => '*************', 'country' => 'GB', 'method' => 'billing'],
    ['session' => 'shipping_test_1', 'ip' => '*************', 'country' => 'CA', 'method' => 'shipping'],
    ['session' => 'billing_test_2', 'ip' => '*************', 'country' => 'AU', 'method' => 'billing'],
    ['session' => 'shipping_test_2', 'ip' => '*************', 'country' => 'DE', 'method' => 'shipping'],
];

foreach ($test_user_data as $data) {
    $result = $database->save_country_cache(
        $data['session'],
        $data['ip'],
        $data['country'],
        $data['method']
    );
    
    echo "<p>Added test data for {$data['method']} detection -> {$data['country']}: " . ($result ? 'Success' : 'Failed') . "</p>";
}

echo "<h2>Current Statistics</h2>";

// Get updated statistics
$admin = new PBC_Admin($pbc_core->database, $pbc_core->logger, $pbc_core->error_handler);
$stats = $admin->get_detection_statistics();

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Metric</th><th>Value</th></tr>";
echo "<tr><td>Total Detections</td><td>" . $stats['total_detections'] . "</td></tr>";
echo "<tr><td>IP Detections</td><td>" . $stats['ip_detections'] . " (" . $stats['ip_percentage'] . "%)</td></tr>";
echo "<tr><td>Address Detections</td><td>" . $stats['address_detections'] . " (" . $stats['address_percentage'] . "%)</td></tr>";
echo "<tr><td>Cache Hit Rate</td><td>" . $stats['cache_hit_rate'] . "%</td></tr>";
echo "</table>";

echo "<h2>Database Table Check</h2>";

// Check if the table exists and has data
global $wpdb;
$table_name = $wpdb->prefix . 'pbc_country_cache';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
echo "<p><strong>Table Exists:</strong> " . ($table_exists ? 'Yes' : 'No') . "</p>";

if ($table_exists) {
    $row_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    echo "<p><strong>Total Rows:</strong> " . $row_count . "</p>";
    
    // Show recent entries
    $recent_entries = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT 10");
    
    if ($recent_entries) {
        echo "<h3>Recent Entries</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Session ID</th><th>IP</th><th>Country</th><th>Method</th><th>Created</th></tr>";
        
        foreach ($recent_entries as $entry) {
            echo "<tr>";
            echo "<td>" . esc_html($entry->session_id) . "</td>";
            echo "<td>" . esc_html($entry->ip_address) . "</td>";
            echo "<td>" . esc_html($entry->country_code) . "</td>";
            echo "<td>" . esc_html($entry->detection_method) . "</td>";
            echo "<td>" . esc_html($entry->created_at) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
}

echo "<p><a href='" . admin_url('admin.php?page=pbc-country-detection') . "'>Go to Country Detection Settings</a></p>";
?>