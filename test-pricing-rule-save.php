<?php
/**
 * Simple test for pricing rule save functionality
 * 
 * Run this script to test if the basic save functionality works
 */

// Load WordPress
require_once('../../../wp-config.php');

// Test product ID (change this to an existing product ID)
$test_product_id = 459; // Use the product ID from the error

echo "<h2>Testing PBC Pricing Rule Save Functionality</h2>\n";

// Check if PBC is loaded
if (!class_exists('PBC_Core')) {
    echo "<p style='color: red;'>ERROR: PBC_Core class not found. Plugin may not be loaded.</p>\n";
    exit;
}

$pbc_core = PBC_Core::get_instance();
if (!$pbc_core) {
    echo "<p style='color: red;'>ERROR: Could not get PBC_Core instance.</p>\n";
    exit;
}

echo "<p style='color: green;'>✓ PBC Core loaded successfully</p>\n";

// Check database connection
if (!$pbc_core->database) {
    echo "<p style='color: red;'>ERROR: PBC Database not available.</p>\n";
    exit;
}

echo "<p style='color: green;'>✓ PBC Database available</p>\n";

// Check if product exists
$product = wc_get_product($test_product_id);
if (!$product) {
    echo "<p style='color: red;'>ERROR: Product ID $test_product_id not found.</p>\n";
    exit;
}

echo "<p style='color: green;'>✓ Test product found: " . $product->get_name() . "</p>\n";

// Test creating a pricing rule
echo "<h3>Testing Rule Creation</h3>\n";

$test_rule_data = array(
    'rule_type' => 'product',
    'object_id' => $test_product_id,
    'country_code' => 'US',
    'adjustment_type' => 'percentage',
    'adjustment_value' => -10.0,
    'is_active' => 1
);

try {
    $rule_id = $pbc_core->database->create_pricing_rule($test_rule_data);
    if ($rule_id) {
        echo "<p style='color: green;'>✓ Test rule created successfully with ID: $rule_id</p>\n";
        
        // Test retrieving the rule
        $retrieved_rules = $pbc_core->database->get_pricing_rules_by_type('product', $test_product_id);
        echo "<p style='color: green;'>✓ Retrieved " . count($retrieved_rules) . " rules for product</p>\n";
        
        // Test updating the rule
        $updated_data = $test_rule_data;
        $updated_data['adjustment_value'] = -15.0;
        $update_result = $pbc_core->database->update_pricing_rule($rule_id, $updated_data);
        
        if ($update_result) {
            echo "<p style='color: green;'>✓ Rule updated successfully</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Rule update failed</p>\n";
        }
        
        // Clean up - delete the test rule
        $delete_result = $pbc_core->database->delete_pricing_rule($rule_id);
        if ($delete_result) {
            echo "<p style='color: green;'>✓ Test rule deleted successfully</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Test rule deletion failed</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>ERROR: Failed to create test rule</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>ERROR: Exception during rule creation: " . $e->getMessage() . "</p>\n";
}

// Test the admin save method directly
echo "<h3>Testing Admin Save Method</h3>\n";

if (!$pbc_core->admin) {
    echo "<p style='color: red;'>ERROR: PBC Admin not available.</p>\n";
} else {
    echo "<p style='color: green;'>✓ PBC Admin available</p>\n";
    
    // Simulate POST data
    $_POST['pbc_country_code'] = array('CA', 'GB');
    $_POST['pbc_adjustment_type'] = array('percentage', 'fixed');
    $_POST['pbc_adjustment_value'] = array('-5', '10');
    $_POST['pbc_is_active'] = array('1', '1');
    $_POST['pbc_rule_id'] = array('', '');
    
    // Create a fake nonce for testing
    $_POST['woocommerce_meta_nonce'] = wp_create_nonce('woocommerce_save_data');
    
    echo "<p>Simulated POST data:</p>\n";
    echo "<pre>" . print_r($_POST, true) . "</pre>\n";
    
    // Test if the method exists
    if (method_exists($pbc_core->admin, 'save_product_pricing_rules')) {
        echo "<p style='color: green;'>✓ save_product_pricing_rules method exists</p>\n";
        
        try {
            // Call the save method directly
            $pbc_core->admin->save_product_pricing_rules($test_product_id);
            echo "<p style='color: green;'>✓ save_product_pricing_rules method executed without errors</p>\n";
            
            // Check if rules were created
            $rules_after_save = $pbc_core->database->get_pricing_rules_by_type('product', $test_product_id);
            echo "<p>Rules after save: " . count($rules_after_save) . "</p>\n";
            
            if (count($rules_after_save) > 0) {
                echo "<p style='color: green;'>✓ Rules were saved successfully!</p>\n";
                foreach ($rules_after_save as $rule) {
                    echo "<p>- Rule ID {$rule->id}: {$rule->country_code} {$rule->adjustment_type} {$rule->adjustment_value}</p>\n";
                }
                
                // Clean up test rules
                foreach ($rules_after_save as $rule) {
                    $pbc_core->database->delete_pricing_rule($rule->id);
                }
                echo "<p>Test rules cleaned up.</p>\n";
            } else {
                echo "<p style='color: red;'>ERROR: No rules were saved</p>\n";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>ERROR: Exception during save: " . $e->getMessage() . "</p>\n";
            echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>\n";
        }
    } else {
        echo "<p style='color: red;'>ERROR: save_product_pricing_rules method does not exist</p>\n";
    }
}

// Check hook registration
echo "<h3>Testing Hook Registration</h3>\n";

global $wp_filter;
if (isset($wp_filter['woocommerce_process_product_meta'])) {
    $hooks = $wp_filter['woocommerce_process_product_meta'];
    echo "<p style='color: green;'>✓ woocommerce_process_product_meta hook has " . count($hooks->callbacks) . " priority levels</p>\n";
    
    $found_pbc_hook = false;
    foreach ($hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && 
                isset($callback['function'][0]) && 
                is_object($callback['function'][0]) && 
                get_class($callback['function'][0]) === 'PBC_Admin') {
                echo "<p style='color: green;'>✓ Found PBC_Admin hook at priority $priority</p>\n";
                $found_pbc_hook = true;
            }
        }
    }
    
    if (!$found_pbc_hook) {
        echo "<p style='color: red;'>ERROR: PBC_Admin hook not found in woocommerce_process_product_meta</p>\n";
    }
} else {
    echo "<p style='color: red;'>ERROR: woocommerce_process_product_meta hook not registered</p>\n";
}

echo "<h3>Test Complete</h3>\n";
?>