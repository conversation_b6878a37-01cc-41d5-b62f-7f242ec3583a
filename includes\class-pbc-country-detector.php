<?php
/**
 * Country detection service for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Country Detector Class
 */
class PBC_Country_Detector {

    /**
     * Detection methods priority order
     */
    const DETECTION_PRIORITY = ['shipping', 'billing', 'ip'];

    /**
     * Cache key prefix for country detection
     */
    const CACHE_PREFIX = 'pbc_country_';

    /**
     * Cache key prefix for IP-based detection
     */
    const IP_CACHE_PREFIX = 'pbc_ip_country_';

    /**
     * Cache key prefix for user-based detection
     */
    const USER_CACHE_PREFIX = 'pbc_user_country_';

    /**
     * Cache expiration time (1 hour)
     */
    const CACHE_EXPIRATION = 3600;

    /**
     * IP cache expiration time (24 hours)
     */
    const IP_CACHE_EXPIRATION = 86400;

    /**
     * User cache expiration time (30 minutes)
     */
    const USER_CACHE_EXPIRATION = 1800;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Constructor
     *
     * @param PBC_Database $database Database instance
     */
    public function __construct($database = null) {
        // Initialize session if not already started
        if (!session_id()) {
            session_start();
        }
        
        $this->error_handler = PBC_Error_Handler::get_instance();
        $this->logger = PBC_Logger::get_instance();
        $this->database = $database;
    }

    /**
     * Detect customer country using specified method or auto-detection
     *
     * @param string $method Detection method ('auto', 'ip', 'billing', 'shipping')
     * @return string Country code (ISO 2-letter)
     */
    public function detect_country($method = 'auto') {
        try {
            // Check for forced country from session first (for AJAX updates)
            $forced_country = $this->get_forced_country();
            if ($forced_country) {
                return $forced_country;
            }

            // Check cache first
            $cached_country = $this->get_cached_country();
            if ($cached_country) {
                return $cached_country;
            }

            $country_code = '';

            if ($method === 'auto') {
                // Use priority order: shipping > billing > IP
                foreach (self::DETECTION_PRIORITY as $detection_method) {
                    try {
                        $country_code = $this->detect_by_method($detection_method);
                        if ($country_code) {
                            break;
                        }
                    } catch (Exception $e) {
                        // Log individual method failure but continue with next method
                        $this->error_handler->handle_general_error($e, [
                            'method' => $detection_method,
                            'function' => __FUNCTION__
                        ], PBC_Error_Handler::SEVERITY_LOW);
                    }
                }
            } else {
                // Use specific method
                $country_code = $this->detect_by_method($method);
            }

            // Fallback to default if no country detected
            if (!$country_code) {
                $country_code = $this->get_default_country();
            }

            // Cache the result
            $this->cache_country($country_code, $method);

            // Log the country detection
            $this->logger->log_country_detection($country_code, $method, [
                'forced' => !empty($this->get_forced_country()),
                'cached' => !empty($this->get_cached_country())
            ]);

            return $country_code;
            
        } catch (Exception $e) {
            // Handle country detection error with fallback
            return $this->error_handler->handle_country_detection_error($e, [
                'method' => $method,
                'cached_country' => $this->get_cached_country(),
                'function' => __FUNCTION__
            ]);
        }
    }

    /**
     * Detect country using specific method
     *
     * @param string $method Detection method
     * @return string|false Country code or false if detection failed
     */
    private function detect_by_method($method) {
        switch ($method) {
            case 'ip':
                return $this->get_country_from_ip();
            case 'billing':
                return $this->get_country_from_billing();
            case 'shipping':
                return $this->get_country_from_shipping();
            default:
                return false;
        }
    }

    /**
     * Get country from IP address using WooCommerce geolocation
     *
     * @param string $ip_address Optional IP address, uses current user IP if not provided
     * @return string|false Country code or false if detection failed
     */
    public function get_country_from_ip($ip_address = null) {
        try {
            // Use WooCommerce geolocation if available
            if (!class_exists('WC_Geolocation')) {
                throw new Exception('WooCommerce geolocation class not available');
            }

            if (!$ip_address) {
                $ip_address = WC_Geolocation::get_ip_address();
                if (!$ip_address) {
                    throw new Exception('Unable to get client IP address');
                }
            }

            // Validate IP address format
            if (!filter_var($ip_address, FILTER_VALIDATE_IP)) {
                throw new Exception("Invalid IP address format: {$ip_address}");
            }

            // Check IP-specific cache first
            $cached_country = $this->get_cached_ip_country($ip_address);
            if ($cached_country !== false) {
                return $cached_country;
            }

            // Get location data from WooCommerce
            $location = WC_Geolocation::geolocate_ip($ip_address);
            
            if (!is_array($location)) {
                throw new Exception('Geolocation service returned invalid data');
            }
            
            if (isset($location['country']) && !empty($location['country'])) {
                $country_code = strtoupper($location['country']);
                
                // Validate country code
                if (!$this->is_valid_country_code($country_code)) {
                    throw new Exception("Invalid country code returned: {$country_code}");
                }
                
                // Cache the IP-based result for longer duration
                $this->cache_ip_country($ip_address, $country_code);
                
                return $country_code;
            }
            
            throw new Exception('No country data in geolocation response');
            
        } catch (Exception $e) {
            $this->error_handler->handle_country_detection_error($e, [
                'ip_address' => $ip_address,
                'method' => 'ip',
                'function' => __FUNCTION__
            ]);
            return false;
        }
    }

    /**
     * Get country from customer's billing address
     *
     * @param int $user_id Optional user ID, uses current user if not provided
     * @return string|false Country code or false if not available
     */
    public function get_country_from_billing($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Check user-specific cache first
        if ($user_id) {
            $cached_country = $this->get_cached_user_country($user_id, 'billing');
            if ($cached_country !== false) {
                return $cached_country;
            }
        }

        $country_code = false;

        // Try to get from user meta first
        if ($user_id) {
            $billing_country = get_user_meta($user_id, 'billing_country', true);
            if ($billing_country) {
                $country_code = strtoupper($billing_country);
            }
        }

        // Try to get from WooCommerce session/customer data
        if (!$country_code && class_exists('WC') && WC()->customer) {
            $billing_country = WC()->customer->get_billing_country();
            if ($billing_country) {
                $country_code = strtoupper($billing_country);
            }
        }

        // Try to get from checkout form data if available
        if (!$country_code && isset($_POST['billing_country']) && !empty($_POST['billing_country'])) {
            $country_code = strtoupper(sanitize_text_field($_POST['billing_country']));
        }

        // Cache the result if we found a country
        if ($country_code && $user_id) {
            $this->cache_user_country($user_id, 'billing', $country_code);
        }

        return $country_code;
    }

    /**
     * Get country from customer's shipping address
     *
     * @param int $user_id Optional user ID, uses current user if not provided
     * @return string|false Country code or false if not available
     */
    public function get_country_from_shipping($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Check user-specific cache first
        if ($user_id) {
            $cached_country = $this->get_cached_user_country($user_id, 'shipping');
            if ($cached_country !== false) {
                return $cached_country;
            }
        }

        $country_code = false;

        // Try to get from user meta first
        if ($user_id) {
            $shipping_country = get_user_meta($user_id, 'shipping_country', true);
            if ($shipping_country) {
                $country_code = strtoupper($shipping_country);
            }
        }

        // Try to get from WooCommerce session/customer data
        if (!$country_code && class_exists('WC') && WC()->customer) {
            $shipping_country = WC()->customer->get_shipping_country();
            if ($shipping_country) {
                $country_code = strtoupper($shipping_country);
            }
        }

        // Try to get from checkout form data if available
        if (!$country_code && isset($_POST['shipping_country']) && !empty($_POST['shipping_country'])) {
            $country_code = strtoupper(sanitize_text_field($_POST['shipping_country']));
        }

        // Cache the result if we found a country
        if ($country_code && $user_id) {
            $this->cache_user_country($user_id, 'shipping', $country_code);
        }

        return $country_code;
    }

    /**
     * Get cached country for current session
     *
     * @return string|false Cached country code or false if not cached
     */
    private function get_cached_country() {
        $session_id = session_id();
        if (!$session_id) {
            return false;
        }

        $cache_key = self::CACHE_PREFIX . $session_id;
        $cached_data = get_transient($cache_key);

        if ($cached_data && is_array($cached_data)) {
            return $cached_data['country_code'];
        }

        return false;
    }

    /**
     * Cache country detection result
     *
     * @param string $country_code Country code to cache
     * @param string $method Detection method used
     */
    private function cache_country($country_code, $method) {
        $session_id = session_id();
        if (!$session_id) {
            return;
        }

        $cache_key = self::CACHE_PREFIX . $session_id;
        $cache_data = [
            'country_code' => $country_code,
            'detection_method' => $method,
            'detected_at' => current_time('timestamp')
        ];

        set_transient($cache_key, $cache_data, self::CACHE_EXPIRATION);
        
        // Also save to database for statistics
        if ($this->database) {
            $this->database->save_country_cache(
                $session_id,
                $this->get_client_ip(),
                $country_code,
                $method
            );
        } else {
            // If no database instance, try to get one from the global core
            global $pbc_core;
            if ($pbc_core && isset($pbc_core->database)) {
                $pbc_core->database->save_country_cache(
                    $session_id,
                    $this->get_client_ip(),
                    $country_code,
                    $method
                );
            }
        }
    }

    /**
     * Get client IP address
     *
     * @return string Client IP address
     */
    private function get_client_ip() {
        // Allow forcing an IP for testing purposes
        if (defined('PBC_FORCE_IP') && PBC_FORCE_IP) {
            return PBC_FORCE_IP;
        }
        // Check for various headers that might contain the real IP
        $ip_headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR even if it's private/reserved
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1';
    }

    /**
     * Get default country from WooCommerce settings
     *
     * @return string Default country code
     */
    private function get_default_country() {
        $default_country = get_option('woocommerce_default_country', 'US');
        
        // Extract country code if it includes state (e.g., 'US:CA' -> 'US')
        if (strpos($default_country, ':') !== false) {
            $default_country = explode(':', $default_country)[0];
        }

        return strtoupper($default_country);
    }

    /**
     * Clear cached country data for current session
     */
    public function clear_cache() {
        $session_id = session_id();
        if ($session_id) {
            $cache_key = self::CACHE_PREFIX . $session_id;
            delete_transient($cache_key);
        }
    }

    /**
     * Get detection result with metadata
     *
     * @param string $method Detection method
     * @return array Detection result with metadata
     */
    public function get_detection_result($method = 'auto') {
        $country_code = $this->detect_country($method);
        $cached_data = $this->get_cached_country_data();

        return [
            'country_code' => $country_code,
            'detection_method' => $cached_data['detection_method'] ?? $method,
            'confidence_level' => $this->get_confidence_level($cached_data['detection_method'] ?? $method),
            'cached' => !empty($cached_data),
            'detected_at' => $cached_data['detected_at'] ?? current_time('timestamp')
        ];
    }

    /**
     * Get cached country data with metadata
     *
     * @return array|false Cached data or false if not cached
     */
    private function get_cached_country_data() {
        $session_id = session_id();
        if (!$session_id) {
            return false;
        }

        $cache_key = self::CACHE_PREFIX . $session_id;
        return get_transient($cache_key);
    }

    /**
     * Get confidence level for detection method
     *
     * @param string $method Detection method
     * @return string Confidence level (high, medium, low)
     */
    private function get_confidence_level($method) {
        switch ($method) {
            case 'shipping':
            case 'billing':
                return 'high';
            case 'ip':
                return 'medium';
            default:
                return 'low';
        }
    }

    /**
     * Force refresh country detection (bypass cache)
     *
     * @param string $method Detection method
     * @return string Country code
     */
    public function refresh_country_detection($method = 'auto') {
        $this->clear_cache();
        return $this->detect_country($method);
    }

    /**
     * Check if country detection is cached
     *
     * @return bool True if cached, false otherwise
     */
    public function is_cached() {
        return $this->get_cached_country() !== false;
    }

    /**
     * Get all available detection methods
     *
     * @return array Available detection methods
     */
    public function get_available_methods() {
        return ['auto', 'ip', 'billing', 'shipping'];
    }

    /**
     * Validate country code format
     *
     * @param string $country_code Country code to validate
     * @return bool True if valid, false otherwise
     */
    public function is_valid_country_code($country_code) {
        return is_string($country_code) && 
               strlen($country_code) === 2 && 
               ctype_alpha($country_code);
    }

    /**
     * Get fallback chain for detection methods
     *
     * @param string $primary_method Primary detection method
     * @return array Fallback chain
     */
    public function get_fallback_chain($primary_method = 'auto') {
        if ($primary_method === 'auto') {
            return self::DETECTION_PRIORITY;
        }

        $chain = [$primary_method];
        foreach (self::DETECTION_PRIORITY as $method) {
            if ($method !== $primary_method) {
                $chain[] = $method;
            }
        }

        return $chain;
    }

    /**
     * Test detection method availability
     *
     * @param string $method Detection method to test
     * @return bool True if method is available, false otherwise
     */
    public function test_detection_method($method) {
        switch ($method) {
            case 'ip':
                return class_exists('WC_Geolocation');
            case 'billing':
            case 'shipping':
                return class_exists('WC') && (is_user_logged_in() || WC()->customer);
            default:
                return false;
        }
    }

    /**
     * Get detection statistics for debugging
     *
     * @return array Detection statistics
     */
    public function get_detection_stats() {
        $stats = [];
        
        foreach ($this->get_available_methods() as $method) {
            if ($method === 'auto') continue;
            
            $stats[$method] = [
                'available' => $this->test_detection_method($method),
                'confidence' => $this->get_confidence_level($method)
            ];
        }

        return $stats;
    }

    /**
     * Get forced country from session (used for AJAX updates)
     *
     * @return string|false Forced country code or false if not set
     */
    private function get_forced_country() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['pbc_forced_country'])) {
            $country = $_SESSION['pbc_forced_country'];
            
            // Clear the forced country after use
            unset($_SESSION['pbc_forced_country']);
            
            if ($this->is_valid_country_code($country)) {
                return strtoupper($country);
            }
        }

        return false;
    }

    /**
     * Set forced country in session (for manual country selection)
     *
     * @param string $country_code Country code to force
     * @return bool True if set successfully, false otherwise
     */
    public function set_forced_country($country_code) {
        if (!$this->is_valid_country_code($country_code)) {
            return false;
        }

        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION['pbc_forced_country'] = strtoupper($country_code);
        
        // Clear existing cache to force re-detection
        $this->clear_cache();
        
        return true;
    }

    /**
     * Get cached country for specific IP address
     *
     * @param string $ip_address IP address
     * @return string|false Cached country code or false if not cached
     */
    private function get_cached_ip_country($ip_address) {
        $cache_key = self::IP_CACHE_PREFIX . md5($ip_address);
        return get_transient($cache_key);
    }

    /**
     * Cache country detection result for IP address
     *
     * @param string $ip_address IP address
     * @param string $country_code Country code to cache
     */
    private function cache_ip_country($ip_address, $country_code) {
        $cache_key = self::IP_CACHE_PREFIX . md5($ip_address);
        set_transient($cache_key, $country_code, self::IP_CACHE_EXPIRATION);
    }

    /**
     * Get cached country for specific user and method
     *
     * @param int $user_id User ID
     * @param string $method Detection method (billing/shipping)
     * @return string|false Cached country code or false if not cached
     */
    private function get_cached_user_country($user_id, $method) {
        $cache_key = self::USER_CACHE_PREFIX . $user_id . '_' . $method;
        return get_transient($cache_key);
    }

    /**
     * Cache country detection result for user and method
     *
     * @param int $user_id User ID
     * @param string $method Detection method (billing/shipping)
     * @param string $country_code Country code to cache
     */
    private function cache_user_country($user_id, $method, $country_code) {
        $cache_key = self::USER_CACHE_PREFIX . $user_id . '_' . $method;
        set_transient($cache_key, $country_code, self::USER_CACHE_EXPIRATION);
    }

    /**
     * Clear all country detection caches
     */
    public function clear_all_cache() {
        global $wpdb;
        
        // Clear session cache
        $this->clear_cache();
        
        // Clear IP cache
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::IP_CACHE_PREFIX . '%'
            )
        );
        
        // Clear user cache
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::USER_CACHE_PREFIX . '%'
            )
        );
    }

    /**
     * Clear cache for specific user
     *
     * @param int $user_id User ID
     */
    public function clear_user_cache($user_id) {
        global $wpdb;
        
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::USER_CACHE_PREFIX . $user_id . '_%'
            )
        );
    }

    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public function get_cache_stats() {
        global $wpdb;
        
        $session_cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::CACHE_PREFIX . '%'
            )
        );
        
        $ip_cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::IP_CACHE_PREFIX . '%'
            )
        );
        
        $user_cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::USER_CACHE_PREFIX . '%'
            )
        );
        
        return [
            'session_cache' => intval($session_cache_count),
            'ip_cache' => intval($ip_cache_count),
            'user_cache' => intval($user_cache_count),
            'total' => intval($session_cache_count) + intval($ip_cache_count) + intval($user_cache_count)
        ];
    }

    /**
     * Warm up cache for frequently accessed IPs and users
     *
     * @param array $ip_addresses Array of IP addresses to warm up
     * @param array $user_ids Array of user IDs to warm up
     * @return int Number of cache entries created
     */
    public function warm_detection_cache($ip_addresses = [], $user_ids = []) {
        $cache_entries = 0;
        
        // Warm up IP cache
        foreach ($ip_addresses as $ip_address) {
            if ($this->get_cached_ip_country($ip_address) === false) {
                $country = $this->get_country_from_ip($ip_address);
                if ($country) {
                    $cache_entries++;
                }
            }
        }
        
        // Warm up user cache
        foreach ($user_ids as $user_id) {
            // Warm billing cache
            if ($this->get_cached_user_country($user_id, 'billing') === false) {
                $country = $this->get_country_from_billing($user_id);
                if ($country) {
                    $cache_entries++;
                }
            }
            
            // Warm shipping cache
            if ($this->get_cached_user_country($user_id, 'shipping') === false) {
                $country = $this->get_country_from_shipping($user_id);
                if ($country) {
                    $cache_entries++;
                }
            }
        }
        
        return $cache_entries;
    }
}