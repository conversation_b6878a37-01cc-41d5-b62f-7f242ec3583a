<!DOCTYPE html>
<html>
  <head>
    <title>Admin Interface Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ccc;
        background: #f9f9f9;
      }
      .button {
        padding: 8px 16px;
        background: #0073aa;
        color: white;
        border: none;
        cursor: pointer;
        margin: 5px;
      }
      .button:hover {
        background: #005a87;
      }
      .button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .results {
        margin-top: 15px;
        padding: 10px;
        background: #fff;
        border: 1px solid #ddd;
      }
      .error {
        color: red;
      }
      .success {
        color: green;
      }
      .debug {
        background: #f0f0f0;
        padding: 10px;
        margin: 10px 0;
        font-family: monospace;
      }
      input {
        padding: 5px;
        margin: 5px;
      }
    </style>
  </head>
  <body>
    <h1>Admin Interface Test</h1>
    <p>
      <strong>Note:</strong> This is a standalone test. For full functionality,
      use the WordPress admin interface.
    </p>

    <div class="test-section">
      <h2>JavaScript Functionality Test</h2>
      <p>
        This tests the JavaScript functions that should be available in the
        admin interface.
      </p>

      <div>
        <label>Test IP:</label>
        <input
          type="text"
          id="test_ip"
          value="*******"
          placeholder="Enter IP address"
        />
        <button id="test_ip_btn" class="button">Test IP Validation</button>
      </div>

      <div>
        <label>Test User ID:</label>
        <input
          type="number"
          id="test_user"
          value="1"
          placeholder="Enter user ID"
        />
        <button id="test_user_btn" class="button">Test User Validation</button>
      </div>

      <div class="results" id="test_results" style="display: none">
        <h3>Test Results</h3>
        <div id="test_output"></div>
      </div>
    </div>

    <div class="test-section">
      <h2>Element Detection Test</h2>
      <p>
        This simulates the elements that should exist in the WordPress admin
        interface.
      </p>

      <!-- Simulate the actual admin elements -->
      <div style="display: none">
        <form id="pbc-country-detection-form">
          <input type="text" id="pbc_test_ip" placeholder="*******" />
          <button type="button" id="pbc_test_ip_detection" class="button">
            Test IP Detection
          </button>

          <input type="number" id="pbc_test_user_id" placeholder="1" />
          <button type="button" id="pbc_test_user_detection" class="button">
            Test User Detection
          </button>

          <div
            id="pbc_test_results"
            class="pbc-test-results"
            style="display: none"
          >
            <h5>Test Results:</h5>
            <div id="pbc_test_output"></div>
          </div>
        </form>
      </div>

      <button id="check_elements" class="button">
        Check for Admin Elements
      </button>
      <div id="element_results" class="results" style="display: none"></div>
    </div>

    <div class="test-section">
      <h2>Debug Console</h2>
      <p>Check the browser console (F12) for debug messages.</p>
      <button id="test_console" class="button">Test Console Logging</button>
      <div class="debug" id="console_output"></div>
    </div>

    <script>
      // Simulate the PBC admin object structure
      var pbc_admin = {
        ajax_url: "/wp-admin/admin-ajax.php",
        nonce: "test_nonce_12345",
        strings: {
          confirm_delete: "Are you sure?",
          error: "An error occurred",
          saving: "Saving...",
        },
      };

      // Simulate the PBC_CountryDetection object
      var PBC_CountryDetection = {
        init: function () {
          console.log("PBC_CountryDetection.init() called");
          this.initTestingTools();
        },

        initTestingTools: function () {
          console.log("PBC: Initializing testing tools");
          console.log(
            "PBC: IP button exists:",
            $("#pbc_test_ip_detection").length > 0
          );
          console.log(
            "PBC: User button exists:",
            $("#pbc_test_user_detection").length > 0
          );

          var self = this;

          // Test IP detection button
          $("#pbc_test_ip_detection")
            .off("click")
            .on("click", function (e) {
              console.log("PBC: IP detection button clicked");
              e.preventDefault();
              e.stopPropagation();

              var testIp = $("#pbc_test_ip").val().trim();
              if (!testIp) {
                alert("Please enter an IP address");
                return false;
              }

              if (!self.isValidIP(testIp)) {
                alert("Please enter a valid IP address");
                return false;
              }

              self.testIpDetection(testIp);
              return false;
            });

          // Test user detection button
          $("#pbc_test_user_detection")
            .off("click")
            .on("click", function (e) {
              console.log("PBC: User detection button clicked");
              e.preventDefault();
              e.stopPropagation();

              var userId = $("#pbc_test_user_id").val().trim();
              if (!userId || isNaN(userId) || parseInt(userId) <= 0) {
                alert("Please enter a valid user ID");
                return false;
              }

              self.testUserDetection(userId);
              return false;
            });
        },

        isValidIP: function (ip) {
          var ipRegex =
            /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
          return ipRegex.test(ip);
        },

        testIpDetection: function (testIp) {
          console.log("PBC: Testing IP detection for:", testIp);
          var $results = $("#pbc_test_results");
          var $output = $("#pbc_test_output");

          $output.html("<div>Simulated test for IP: " + testIp + "</div>");
          $results.show();

          // Simulate AJAX call
          setTimeout(function () {
            $output.html(
              '<div class="success">' +
                "<h5>Simulated Success</h5>" +
                "<p><strong>IP:</strong> " +
                testIp +
                "</p>" +
                "<p><strong>Country:</strong> United States (US)</p>" +
                "<p><strong>Method:</strong> ip</p>" +
                "<p><em>Note: This is a simulation. Real functionality requires WordPress admin.</em></p>" +
                "</div>"
            );
          }, 1000);
        },

        testUserDetection: function (userId) {
          console.log("PBC: Testing user detection for:", userId);
          var $results = $("#pbc_test_results");
          var $output = $("#pbc_test_output");

          $output.html("<div>Simulated test for User ID: " + userId + "</div>");
          $results.show();

          // Simulate AJAX call
          setTimeout(function () {
            $output.html(
              '<div class="success">' +
                "<h5>Simulated Success</h5>" +
                "<p><strong>User ID:</strong> " +
                userId +
                "</p>" +
                "<p><strong>Billing:</strong> United Kingdom (GB)</p>" +
                "<p><strong>Shipping:</strong> Canada (CA)</p>" +
                "<p><em>Note: This is a simulation. Real functionality requires WordPress admin.</em></p>" +
                "</div>"
            );
          }, 1000);
        },
      };

      jQuery(document).ready(function ($) {
        console.log("Test page loaded");

        // Test basic IP validation
        $("#test_ip_btn").on("click", function () {
          var ip = $("#test_ip").val().trim();
          var isValid = PBC_CountryDetection.isValidIP(ip);

          $("#test_results").show();
          $("#test_output").html(
            "<p><strong>IP:</strong> " +
              ip +
              "</p>" +
              "<p><strong>Valid:</strong> " +
              (isValid ? "✅ Yes" : "❌ No") +
              "</p>"
          );
        });

        // Test user ID validation
        $("#test_user_btn").on("click", function () {
          var userId = $("#test_user").val().trim();
          var isValid = !isNaN(userId) && parseInt(userId) > 0;

          $("#test_results").show();
          $("#test_output").html(
            "<p><strong>User ID:</strong> " +
              userId +
              "</p>" +
              "<p><strong>Valid:</strong> " +
              (isValid ? "✅ Yes" : "❌ No") +
              "</p>"
          );
        });

        // Check for admin elements
        $("#check_elements").on("click", function () {
          var elements = {
            Form: "#pbc-country-detection-form",
            "IP Input": "#pbc_test_ip",
            "IP Button": "#pbc_test_ip_detection",
            "User Input": "#pbc_test_user_id",
            "User Button": "#pbc_test_user_detection",
            "Results Area": "#pbc_test_results",
            "Output Area": "#pbc_test_output",
          };

          var html = "<h4>Element Detection Results:</h4>";
          for (var name in elements) {
            var selector = elements[name];
            var exists = $(selector).length > 0;
            html +=
              "<p><strong>" +
              name +
              " (" +
              selector +
              "):</strong> " +
              (exists ? "✅ Found" : "❌ Missing") +
              "</p>";
          }

          $("#element_results").html(html).show();
        });

        // Test console logging
        $("#test_console").on("click", function () {
          console.log("PBC: Test console message");
          console.log("PBC: pbc_admin object:", pbc_admin);
          console.log(
            "PBC: PBC_CountryDetection object:",
            PBC_CountryDetection
          );

          $("#console_output").html(
            "Console messages sent. Check browser console (F12) to see:<br>" +
              "• PBC: Test console message<br>" +
              "• PBC: pbc_admin object: [object]<br>" +
              "• PBC: PBC_CountryDetection object: [object]"
          );
        });

        // Initialize the country detection if form exists
        if ($("#pbc-country-detection-form").length > 0) {
          console.log("PBC: Form found, initializing...");
          PBC_CountryDetection.init();
        } else {
          console.log("PBC: Form not found");
        }

        // Also initialize test detection buttons if they exist (fallback)
        if (
          $("#pbc_test_ip_detection").length > 0 ||
          $("#pbc_test_user_detection").length > 0
        ) {
          console.log("PBC: Initializing test detection buttons as fallback");
          PBC_CountryDetection.initTestingTools();
        }

        // Document-level fallback handlers
        $(document).on("click", "#pbc_test_ip_detection", function (e) {
          console.log("PBC: Fallback IP detection handler triggered");
          e.preventDefault();
          e.stopPropagation();

          if (
            typeof PBC_CountryDetection !== "undefined" &&
            PBC_CountryDetection.testIpDetection
          ) {
            var testIp = $("#pbc_test_ip").val().trim();
            if (testIp && PBC_CountryDetection.isValidIP(testIp)) {
              PBC_CountryDetection.testIpDetection(testIp);
            } else {
              alert("Please enter a valid IP address");
            }
          }
          return false;
        });

        $(document).on("click", "#pbc_test_user_detection", function (e) {
          console.log("PBC: Fallback User detection handler triggered");
          e.preventDefault();
          e.stopPropagation();

          if (
            typeof PBC_CountryDetection !== "undefined" &&
            PBC_CountryDetection.testUserDetection
          ) {
            var userId = $("#pbc_test_user_id").val().trim();
            if (userId && !isNaN(userId) && parseInt(userId) > 0) {
              PBC_CountryDetection.testUserDetection(userId);
            } else {
              alert("Please enter a valid user ID");
            }
          }
          return false;
        });
      });
    </script>
  </body>
</html>
