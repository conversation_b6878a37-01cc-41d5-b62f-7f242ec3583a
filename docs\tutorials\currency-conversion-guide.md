# Currency Conversion Guide

## Overview

The Currency Conversion feature allows your WooCommerce store to automatically display prices in customers' local currencies based on their country location. This enhances the shopping experience by showing familiar currency formats and reducing confusion about pricing.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Configuration](#configuration)
3. [Exchange Rate Providers](#exchange-rate-providers)
4. [Country-Currency Mappings](#country-currency-mappings)
5. [Testing & Troubleshooting](#testing--troubleshooting)
6. [Advanced Settings](#advanced-settings)
7. [Best Practices](#best-practices)
8. [FAQ](#faq)

## Getting Started

### Prerequisites

- WooCommerce 5.0 or higher
- Price by Country plugin installed and activated
- At least one pricing rule configured (product, category, or global)

### Quick Setup

1. **Navigate to Settings**

   - Go to `WooCommerce → Price by Country → Settings`
   - Scroll down to the **Currency Settings** section

2. **Enable Currency Conversion**

   - Check the box for "Enable Currency Conversion"
   - This activates automatic currency conversion based on customer location

3. **Save Settings**
   - Click "Save Settings" to apply your changes

That's it! Your store will now automatically convert prices to customers' local currencies.

## Configuration

### Basic Settings

#### Enable Currency Conversion

- **Purpose**: Toggles the entire currency conversion system
- **Default**: Disabled
- **Impact**: When enabled, all prices are automatically converted based on customer country

#### Show Original Price

- **Purpose**: Displays both original and converted prices to customers
- **Default**: Disabled
- **Example**: "$10.00 USD (€8.50 EUR)"
- **Use Case**: Helpful for transparency and building customer trust

#### ExchangeRate-API Key

- **Purpose**: Provides access to premium exchange rate data
- **Default**: Empty (uses free tier)
- **Free Tier**: 1,500 requests per month
- **Paid Plans**: Higher limits and additional features
- **Get Key**: [ExchangeRate-API.com](https://exchangerate-api.com/)

### Currency Display Examples

| Customer Country | Store Currency | Display Currency | Example Price |
| ---------------- | -------------- | ---------------- | ------------- |
| United States    | USD            | USD              | $19.99        |
| United Kingdom   | USD            | GBP              | £15.99        |
| Germany          | USD            | EUR              | €17.49        |
| Japan            | USD            | JPY              | ¥2,199        |
| Canada           | USD            | CAD              | $24.99 CAD    |

## Exchange Rate Providers

The plugin uses multiple exchange rate providers to ensure reliability and accuracy.

### Available Providers

#### 1. ExchangeRate-API

- **Type**: Primary provider
- **Coverage**: 170+ currencies
- **Update Frequency**: Real-time
- **Free Tier**: 1,500 requests/month
- **Reliability**: ⭐⭐⭐⭐⭐

#### 2. European Central Bank (ECB)

- **Type**: Backup provider
- **Coverage**: 30+ major currencies
- **Update Frequency**: Daily (weekdays)
- **Cost**: Completely free
- **Reliability**: ⭐⭐⭐⭐⭐

#### 3. Bank of Canada

- **Type**: Backup provider
- **Coverage**: 20+ currencies
- **Update Frequency**: Daily
- **Cost**: Completely free
- **Reliability**: ⭐⭐⭐⭐

### Provider Fallback System

The plugin automatically tries providers in order:

1. **ExchangeRate-API** (if API key provided)
2. **ExchangeRate-API** (free tier)
3. **European Central Bank**
4. **Bank of Canada**

If all providers fail, the original price is displayed without conversion.

### Testing Providers

1. Go to `WooCommerce → Price by Country → Settings`
2. Scroll to **Currency Settings**
3. Click **"Test Exchange Rate Providers"**
4. Review the test results:
   - ✅ **Online**: Provider is working correctly
   - ❌ **Offline**: Provider is experiencing issues
   - **Response Time**: How quickly the provider responds

## Country-Currency Mappings

The plugin includes 100+ default country-to-currency mappings, but you can customize these as needed.

### Default Mappings (Examples)

| Country        | Currency          | Code |
| -------------- | ----------------- | ---- |
| United States  | US Dollar         | USD  |
| United Kingdom | British Pound     | GBP  |
| Germany        | Euro              | EUR  |
| Japan          | Japanese Yen      | JPY  |
| Canada         | Canadian Dollar   | CAD  |
| Australia      | Australian Dollar | AUD  |
| Switzerland    | Swiss Franc       | CHF  |
| China          | Chinese Yuan      | CNY  |
| India          | Indian Rupee      | INR  |
| Brazil         | Brazilian Real    | BRL  |

### Custom Mappings

To override default mappings:

1. **Access Settings**

   - Go to `WooCommerce → Price by Country → Settings`
   - Find the **Currency Settings** section

2. **Add Custom Mapping** (Future Feature)
   - Select country from dropdown
   - Choose desired currency
   - Save settings

**Note**: Custom mapping interface is planned for a future update. Currently, the plugin uses comprehensive default mappings.

## Testing & Troubleshooting

### Testing Currency Conversion

#### Method 1: Provider Test

1. Go to **Currency Settings**
2. Click **"Test Exchange Rate Providers"**
3. Verify all providers show "Online" status

#### Method 2: Frontend Test

1. **Change Your Location**:
   - Use VPN to simulate different countries
   - Or modify your WooCommerce billing address
2. **View Product Pages**:
   - Prices should automatically convert
   - Currency symbols should update

#### Method 3: Browser Developer Tools

1. Open browser developer tools
2. Go to **Network** tab
3. Look for exchange rate API calls
4. Verify successful responses (200 status)

### Common Issues & Solutions

#### Issue: Prices Not Converting

**Possible Causes**:

- Currency conversion disabled
- All exchange rate providers offline
- Customer country not detected

**Solutions**:

1. Verify "Enable Currency Conversion" is checked
2. Test exchange rate providers
3. Check country detection settings

#### Issue: Wrong Currency Displayed

**Possible Causes**:

- Incorrect country detection
- Wrong country-currency mapping

**Solutions**:

1. Test country detection accuracy
2. Verify customer's actual location
3. Check if custom mappings are needed

#### Issue: Slow Page Loading

**Possible Causes**:

- Exchange rate API timeouts
- Cache not working properly

**Solutions**:

1. Check provider response times
2. Clear currency cache
3. Verify cache is enabled

### Cache Management

#### Automatic Caching

- **Duration**: 24 hours
- **Purpose**: Reduces API calls and improves performance
- **Scope**: Per currency pair (e.g., USD to EUR)

#### Manual Cache Control

1. **Clear Cache**:

   - Go to **Currency Settings**
   - Click **"Clear Currency Cache"**
   - Use when exchange rates seem outdated

2. **Cache Status**:
   - View cached exchange rates count
   - Monitor cache hit/miss ratios

## Advanced Settings

### Performance Optimization

#### Cache Warming

The plugin automatically warms the cache for popular currency pairs:

- USD, EUR, GBP, JPY, CAD, AUD
- Reduces first-visitor latency
- Runs automatically in background

#### Batch Processing

- Multiple currency conversions processed together
- Reduces API calls for cart/checkout pages
- Improves performance on product listing pages

### API Rate Limiting

#### Free Tier Management

- **Limit**: 1,500 requests/month
- **Strategy**: Aggressive caching (24 hours)
- **Fallback**: ECB and Bank of Canada

#### Paid Tier Benefits

- **Higher Limits**: Up to 100,000+ requests/month
- **Historical Data**: Access to past exchange rates
- **Priority Support**: Faster response times

### Integration with Other Plugins

#### WooCommerce Compatibility

- ✅ **WooCommerce Subscriptions**: Recurring payments in local currency
- ✅ **WooCommerce Bookings**: Booking prices converted
- ✅ **WooCommerce Memberships**: Membership fees in local currency
- ✅ **HPOS (High-Performance Order Storage)**: Full compatibility

#### Payment Gateway Considerations

- **Important**: Currency conversion is for display only
- **Actual Charges**: Still processed in your store's base currency
- **Customer Communication**: Make this clear during checkout

## Best Practices

### 1. Currency Selection Strategy

#### Recommended Approach

- **Major Markets**: Use local currencies (USD, EUR, GBP, JPY)
- **Smaller Markets**: Consider regional currencies or USD
- **Tourist Destinations**: Offer multiple currency options

#### Avoid Over-Segmentation

- Don't create too many currency variations
- Focus on your primary customer markets
- Consider maintenance overhead

### 2. Pricing Strategy

#### Psychological Pricing

- **Round Numbers**: Convert $19.99 USD to €18.00 EUR instead of €17.49
- **Local Conventions**: Respect local pricing patterns
- **Competitive Analysis**: Research local market prices

#### Legal Considerations

- **Tax Implications**: Understand local tax requirements
- **Regulatory Compliance**: Some countries have specific pricing laws
- **Consumer Protection**: Be transparent about currency conversion

### 3. Customer Communication

#### Transparency

- Clearly indicate when prices are converted
- Show original currency when helpful
- Explain conversion rates and timing

#### Checkout Process

- Remind customers about actual billing currency
- Provide conversion rate at time of purchase
- Include currency conversion in order confirmation

### 4. Monitoring & Maintenance

#### Regular Checks

- **Weekly**: Test exchange rate providers
- **Monthly**: Review conversion accuracy
- **Quarterly**: Analyze customer feedback

#### Performance Monitoring

- Monitor page load times
- Track API usage and costs
- Review cache hit rates

## FAQ

### General Questions

**Q: Does currency conversion affect the actual payment amount?**
A: No, currency conversion is for display purposes only. Customers are charged in your store's base currency, but their payment processor may apply their own conversion rates.

**Q: How often are exchange rates updated?**
A: Exchange rates are cached for 24 hours. The plugin fetches fresh rates when the cache expires or when manually cleared.

**Q: What happens if all exchange rate providers are down?**
A: The plugin gracefully falls back to displaying prices in your store's base currency without conversion.

### Technical Questions

**Q: Can I customize the country-to-currency mappings?**
A: The plugin includes comprehensive default mappings for 100+ countries. Custom mapping interface is planned for a future update.

**Q: How does caching work?**
A: Exchange rates are cached for 24 hours using WordPress transients. Both direct rates (USD→EUR) and reverse rates (EUR→USD) are cached automatically.

**Q: Is there a limit on API requests?**
A: The free tier of ExchangeRate-API allows 1,500 requests/month. The plugin's caching system minimizes API usage. Paid plans offer higher limits.

### Troubleshooting Questions

**Q: Why are prices not converting for some customers?**
A: This could be due to:

- Country detection issues
- Exchange rate provider problems
- Currency conversion disabled
- Customer's country not in the mapping list

**Q: How can I test if currency conversion is working?**
A: Use the built-in provider test feature in the Currency Settings, or change your billing address to a different country and check product prices.

**Q: What should I do if exchange rates seem incorrect?**
A: Clear the currency cache to fetch fresh rates, or test the exchange rate providers to ensure they're responding correctly.

## Support

### Getting Help

1. **Documentation**: Check this guide and other plugin documentation
2. **Provider Test**: Use the built-in testing tools
3. **Support Forum**: Contact plugin support with specific issues
4. **Debug Mode**: Enable WordPress debug mode for detailed error logs

### Reporting Issues

When reporting currency conversion issues, please include:

- WordPress and WooCommerce versions
- Plugin version
- Exchange rate provider test results
- Specific currency pairs affected
- Error messages (if any)

---

_Last updated: [Current Date]_
_Plugin version: 1.0.0_
