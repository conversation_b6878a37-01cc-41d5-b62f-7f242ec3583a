<?php
/**
 * Test if PBC product pricing fields are being rendered
 * 
 * This script checks if the pricing fields are properly added to the product edit page
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h2>Testing PBC Product Fields Rendering</h2>\n";

// Check if PBC is loaded
if (!class_exists('PBC_Core')) {
    echo "<p style='color: red;'>ERROR: PBC_Core class not found. Plugin may not be loaded.</p>\n";
    exit;
}

$pbc_core = PBC_Core::get_instance();
if (!$pbc_core || !$pbc_core->admin) {
    echo "<p style='color: red;'>ERROR: PBC Admin not available.</p>\n";
    exit;
}

echo "<p style='color: green;'>✓ PBC Admin available</p>\n";

// Check if the hook is registered
global $wp_filter;
$hook_name = 'woocommerce_product_options_pricing';

if (isset($wp_filter[$hook_name])) {
    $hooks = $wp_filter[$hook_name];
    echo "<p style='color: green;'>✓ $hook_name hook has " . count($hooks->callbacks) . " priority levels</p>\n";
    
    $found_pbc_hook = false;
    foreach ($hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && 
                isset($callback['function'][0]) && 
                is_object($callback['function'][0]) && 
                get_class($callback['function'][0]) === 'PBC_Admin') {
                echo "<p style='color: green;'>✓ Found PBC_Admin hook at priority $priority</p>\n";
                $found_pbc_hook = true;
                
                // Check the method name
                if (isset($callback['function'][1])) {
                    echo "<p>Method: " . $callback['function'][1] . "</p>\n";
                }
            }
        }
    }
    
    if (!$found_pbc_hook) {
        echo "<p style='color: red;'>ERROR: PBC_Admin hook not found in $hook_name</p>\n";
    }
} else {
    echo "<p style='color: red;'>ERROR: $hook_name hook not registered</p>\n";
}

// Test the method directly
echo "<h3>Testing add_product_pricing_fields Method</h3>\n";

if (method_exists($pbc_core->admin, 'add_product_pricing_fields')) {
    echo "<p style='color: green;'>✓ add_product_pricing_fields method exists</p>\n";
    
    // Create a fake post for testing
    global $post;
    $test_product_id = 459; // Use the product ID from the error
    $post = get_post($test_product_id);
    
    if ($post) {
        echo "<p style='color: green;'>✓ Test product loaded: " . $post->post_title . "</p>\n";
        
        // Capture the output of the method
        ob_start();
        try {
            $pbc_core->admin->add_product_pricing_fields();
            $output = ob_get_clean();
            
            if (!empty($output)) {
                echo "<p style='color: green;'>✓ Method produced output (" . strlen($output) . " characters)</p>\n";
                echo "<h4>Sample Output (first 500 chars):</h4>\n";
                echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "...</pre>\n";
                
                // Check for key elements
                if (strpos($output, 'pbc-product-pricing') !== false) {
                    echo "<p style='color: green;'>✓ Contains pbc-product-pricing class</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ Missing pbc-product-pricing class</p>\n";
                }
                
                if (strpos($output, 'pbc-add-product-rule') !== false) {
                    echo "<p style='color: green;'>✓ Contains add rule button</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ Missing add rule button</p>\n";
                }
                
                if (strpos($output, 'pbc-product-rule-template') !== false) {
                    echo "<p style='color: green;'>✓ Contains rule template</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ Missing rule template</p>\n";
                }
                
            } else {
                echo "<p style='color: red;'>ERROR: Method produced no output</p>\n";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>ERROR: Exception in add_product_pricing_fields: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>ERROR: Could not load test product</p>\n";
    }
} else {
    echo "<p style='color: red;'>ERROR: add_product_pricing_fields method does not exist</p>\n";
}

// Check if WooCommerce product edit page hooks are working
echo "<h3>Testing WooCommerce Integration</h3>\n";

if (class_exists('WooCommerce')) {
    echo "<p style='color: green;'>✓ WooCommerce is active</p>\n";
    
    // Check if we're in admin
    if (is_admin()) {
        echo "<p style='color: green;'>✓ Running in admin context</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ Not running in admin context</p>\n";
    }
    
    // Check if the current user can edit products
    if (current_user_can('edit_products')) {
        echo "<p style='color: green;'>✓ Current user can edit products</p>\n";
    } else {
        echo "<p style='color: red;'>ERROR: Current user cannot edit products</p>\n";
    }
    
} else {
    echo "<p style='color: red;'>ERROR: WooCommerce is not active</p>\n";
}

echo "<h3>Test Complete</h3>\n";
?>