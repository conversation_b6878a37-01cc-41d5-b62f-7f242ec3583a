<?php
/**
 * Pricing engine class for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Pricing Engine Class
 */
class PBC_Pricing_Engine {

    /**
     * Rule priority hierarchy (higher number = higher priority)
     */
    const RULE_PRIORITY = [
        'global' => 1,
        'category' => 2,
        'product' => 3
    ];

    /**
     * Cache key prefix for price calculations
     */
    const CACHE_PREFIX = 'pbc_price_';

    /**
     * Cache key prefix for pricing rules
     */
    const RULES_CACHE_PREFIX = 'pbc_rules_';

    /**
     * Cache key prefix for batch calculations
     */
    const BATCH_CACHE_PREFIX = 'pbc_batch_';

    /**
     * Cache expiration time (30 minutes)
     */
    const CACHE_EXPIRATION = 1800;

    /**
     * Rules cache expiration time (1 hour)
     */
    const RULES_CACHE_EXPIRATION = 3600;

    /**
     * Batch cache expiration time (15 minutes)
     */
    const BATCH_CACHE_EXPIRATION = 900;

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Country detector instance
     *
     * @var PBC_Country_Detector
     */
    private $country_detector;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Currency exchange instance
     *
     * @var PBC_Currency_Exchange
     */
    private $currency_exchange;

    /**
     * Constructor
     *
     * @param PBC_Database $database Database instance
     * @param PBC_Country_Detector $country_detector Country detector instance
     */
    public function __construct($database, $country_detector) {
        $this->database = $database;
        $this->country_detector = $country_detector;
        $this->currency_exchange = new PBC_Currency_Exchange();
        $this->error_handler = PBC_Error_Handler::get_instance();
        $this->logger = PBC_Logger::get_instance();
    }

    /**
     * Get price adjustment for a product in a specific country
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code (optional, will detect if not provided)
     * @param float $base_price Base price to adjust (optional, will get from product if not provided)
     * @return array Price calculation result
     */
    public function get_price_adjustment($product_id, $country_code = null, $base_price = null) {
        try {
            $product_id = intval($product_id);
            
            // Validate product ID
            if ($product_id <= 0) {
                throw new InvalidArgumentException('Invalid product ID provided');
            }
            
            // Detect country if not provided
            if (!$country_code) {
                $country_code = $this->country_detector->detect_country();
            }
            
            // Validate country code
            if (!$this->country_detector->is_valid_country_code($country_code)) {
                return $this->create_price_result($base_price, $base_price, 0, 'none', $country_code, 'none');
            }

            // Check cache first
            $cached_result = $this->get_cached_price($product_id, $country_code);
            if ($cached_result !== false) {
                return $cached_result;
            }

            // Get base price if not provided
            if ($base_price === null) {
                $base_price = $this->get_product_base_price($product_id);
                if ($base_price === false) {
                    throw new Exception('Unable to retrieve product base price');
                }
            }

            // Check if pricing rules are enabled
            if (get_option('pbc_enable_pricing_rules', true)) {
                // Find applicable pricing rule using hierarchy
                $rule = $this->find_applicable_rule($product_id, $country_code);

                if (!$rule) {
                    // No rule found, return original price
                    $result = $this->create_price_result($base_price, $base_price, 0, 'none', $country_code, 'none');
                } else {
                    // Calculate adjusted price
                    $adjusted_price = $this->calculate_adjusted_price($base_price, $rule);
                    $adjustment_amount = $adjusted_price - $base_price;

                    $result = $this->create_price_result(
                        $base_price,
                        $adjusted_price,
                        $adjustment_amount,
                        $rule->adjustment_type,
                        $country_code,
                        $rule->rule_type
                    );
                }
            } else {
                // Pricing rules disabled, return original price
                $result = $this->create_price_result($base_price, $base_price, 0, 'disabled', $country_code, 'none');
            }

            // Always apply currency conversion if enabled (regardless of whether pricing rules exist)
            $result = $this->apply_currency_conversion($result, $country_code);

            // Cache the result
            $this->cache_price_result($product_id, $country_code, $result);

            // Log the price calculation
            $this->logger->log_price_calculation($product_id, $country_code, $result);

            return $result;
            
        } catch (Exception $e) {
            // Handle pricing calculation error
            return $this->error_handler->handle_pricing_error($e, [
                'product_id' => $product_id,
                'country_code' => $country_code,
                'base_price' => $base_price,
                'function' => __FUNCTION__
            ]);
        }
    }

    /**
     * Get country-specific price for a product (simplified version)
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return float Adjusted price
     */
    public function get_country_price($product_id, $country_code) {
        $result = $this->get_price_adjustment($product_id, $country_code);
        return $result['adjusted_price'];
    }

    /**
     * Find applicable pricing rule using hierarchy (product > category > global)
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return object|null Pricing rule or null if none found
     */
    private function find_applicable_rule($product_id, $country_code) {
        try {
            // Check cache first
            $cached_rule = $this->get_cached_rule($product_id, $country_code);
            if ($cached_rule !== false) {
                return $cached_rule;
            }

            // 1. Check for product-specific rule (highest priority)
            $product_rule = $this->database->get_pricing_rule_by_criteria('product', $product_id, $country_code);
            if ($product_rule) {
                $this->cache_rule($product_id, $country_code, $product_rule);
                return $product_rule;
            }

            // 2. Check for category rules (medium priority)
            $category_rule = $this->find_category_rule($product_id, $country_code);
            if ($category_rule) {
                $this->cache_rule($product_id, $country_code, $category_rule);
                return $category_rule;
            }

            // 3. Check for global rule (lowest priority)
            $global_rule = $this->database->get_pricing_rule_by_criteria('global', null, $country_code);
            if ($global_rule) {
                $this->cache_rule($product_id, $country_code, $global_rule);
                return $global_rule;
            }

            // Cache null result to prevent repeated database queries
            $this->cache_rule($product_id, $country_code, null);
            return null;
            
        } catch (Exception $e) {
            $this->error_handler->handle_pricing_error($e, [
                'product_id' => $product_id,
                'country_code' => $country_code,
                'function' => __FUNCTION__
            ]);
            return null;
        }
    }

    /**
     * Find category rule for a product
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return object|null Category rule or null if none found
     */
    private function find_category_rule($product_id, $country_code) {
        // Get product categories
        $category_ids = $this->get_product_category_ids($product_id);
        
        if (empty($category_ids)) {
            return null;
        }

        // Check each category for pricing rules
        // If multiple categories have rules, use the first one found
        foreach ($category_ids as $category_id) {
            $category_rule = $this->database->get_pricing_rule_by_criteria('category', $category_id, $country_code);
            if ($category_rule) {
                return $category_rule;
            }
        }

        return null;
    }

    /**
     * Calculate adjusted price based on rule
     *
     * @param float $base_price Base price
     * @param object $rule Pricing rule
     * @return float Adjusted price
     */
    private function calculate_adjusted_price($base_price, $rule) {
        $base_price = floatval($base_price);
        $adjustment_value = floatval($rule->adjustment_value);

        switch ($rule->adjustment_type) {
            case 'fixed':
                return max(0, $base_price + $adjustment_value);
            
            case 'percentage':
                $multiplier = 1 + ($adjustment_value / 100);
                return max(0, $base_price * $multiplier);
            
            default:
                error_log("PBC: Unknown adjustment type: {$rule->adjustment_type}");
                return $base_price;
        }
    }

    /**
     * Create price calculation result object
     *
     * @param float $original_price Original price
     * @param float $adjusted_price Adjusted price
     * @param float $adjustment_amount Adjustment amount
     * @param string $adjustment_type Adjustment type
     * @param string $country_code Country code
     * @param string $rule_source Rule source
     * @return array Price result
     */
    private function create_price_result($original_price, $adjusted_price, $adjustment_amount, $adjustment_type, $country_code, $rule_source) {
        return [
            'original_price' => floatval($original_price),
            'adjusted_price' => floatval($adjusted_price),
            'adjustment_amount' => floatval($adjustment_amount),
            'adjustment_type' => $adjustment_type,
            'country_code' => $country_code,
            'rule_source' => $rule_source,
            'currency_code' => get_woocommerce_currency(),
            'calculated_at' => current_time('timestamp')
        ];
    }

    /**
     * Get product base price
     *
     * @param int $product_id Product ID
     * @return float|false Base price or false on error
     */
    private function get_product_base_price($product_id) {
        try {
            if (!function_exists('wc_get_product')) {
                throw new Exception('WooCommerce is not available');
            }

            $product = wc_get_product($product_id);
            if (!$product) {
                throw new Exception("Product with ID {$product_id} not found");
            }

            // Get regular price (not sale price) as base
            $price = $product->get_regular_price();
            
            if ($price === '' || $price === null) {
                throw new Exception("Product {$product_id} has no regular price set");
            }
            
            return floatval($price);
            
        } catch (Exception $e) {
            $this->error_handler->handle_pricing_error($e, [
                'product_id' => $product_id,
                'function' => __FUNCTION__
            ]);
            return false;
        }
    }

    /**
     * Get product category IDs
     *
     * @param int $product_id Product ID
     * @return array Category IDs
     */
    private function get_product_category_ids($product_id) {
        $terms = wp_get_post_terms($product_id, 'product_cat', ['fields' => 'ids']);
        
        if (is_wp_error($terms)) {
            return [];
        }

        return array_map('intval', $terms);
    }

    /**
     * Get cached price result
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return array|false Cached result or false if not cached
     */
    private function get_cached_price($product_id, $country_code) {
        $cache_key = self::CACHE_PREFIX . $product_id . '_' . $country_code;
        return get_transient($cache_key);
    }

    /**
     * Cache price calculation result
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @param array $result Price calculation result
     */
    private function cache_price_result($product_id, $country_code, $result) {
        $cache_key = self::CACHE_PREFIX . $product_id . '_' . $country_code;
        set_transient($cache_key, $result, self::CACHE_EXPIRATION);
    }

    /**
     * Clear price cache for specific product and country
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code (optional, clears all countries if not provided)
     */
    public function clear_price_cache($product_id, $country_code = null) {
        if ($country_code) {
            $cache_key = self::CACHE_PREFIX . $product_id . '_' . $country_code;
            delete_transient($cache_key);
        } else {
            // Clear all cached prices for this product
            // This is a simplified approach - in production, you might want to track cache keys
            global $wpdb;
            $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                    '_transient_' . self::CACHE_PREFIX . $product_id . '_%'
                )
            );
        }
    }

    /**
     * Clear all price cache
     */
    public function clear_all_price_cache() {
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s OR option_name LIKE %s",
                '_transient_' . self::CACHE_PREFIX . '%',
                '_transient_' . self::RULES_CACHE_PREFIX . '%',
                '_transient_' . self::BATCH_CACHE_PREFIX . '%'
            )
        );
    }

    /**
     * Get cached pricing rule
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return object|false|null Cached rule, false if not cached, null if no rule exists
     */
    private function get_cached_rule($product_id, $country_code) {
        $cache_key = self::RULES_CACHE_PREFIX . $product_id . '_' . $country_code;
        return get_transient($cache_key);
    }

    /**
     * Cache pricing rule result
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @param object|null $rule Pricing rule or null if no rule
     */
    private function cache_rule($product_id, $country_code, $rule) {
        $cache_key = self::RULES_CACHE_PREFIX . $product_id . '_' . $country_code;
        set_transient($cache_key, $rule, self::RULES_CACHE_EXPIRATION);
    }

    /**
     * Clear rule cache for specific product and country
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code (optional, clears all countries if not provided)
     */
    public function clear_rule_cache($product_id, $country_code = null) {
        if ($country_code) {
            $cache_key = self::RULES_CACHE_PREFIX . $product_id . '_' . $country_code;
            delete_transient($cache_key);
        } else {
            // Clear all cached rules for this product
            global $wpdb;
            $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                    '_transient_' . self::RULES_CACHE_PREFIX . $product_id . '_%'
                )
            );
        }
    }

    /**
     * Get multiple price adjustments for batch processing
     *
     * @param array $product_ids Array of product IDs
     * @param string $country_code Country code
     * @return array Array of price results keyed by product ID
     */
    public function get_batch_price_adjustments($product_ids, $country_code = null) {
        // Detect country if not provided
        if (!$country_code) {
            $country_code = $this->country_detector->detect_country();
        }

        // Check batch cache first
        $batch_cache_key = self::BATCH_CACHE_PREFIX . md5(implode(',', $product_ids) . '_' . $country_code);
        $cached_batch = get_transient($batch_cache_key);
        if ($cached_batch !== false) {
            return $cached_batch;
        }

        $results = [];
        $uncached_products = [];

        // Check individual caches first
        foreach ($product_ids as $product_id) {
            $cached_result = $this->get_cached_price($product_id, $country_code);
            if ($cached_result !== false) {
                $results[$product_id] = $cached_result;
            } else {
                $uncached_products[] = $product_id;
            }
        }

        // Process uncached products
        foreach ($uncached_products as $product_id) {
            $results[$product_id] = $this->get_price_adjustment($product_id, $country_code);
        }

        // Cache the batch result
        set_transient($batch_cache_key, $results, self::BATCH_CACHE_EXPIRATION);

        return $results;
    }

    /**
     * Warm up cache for frequently accessed products
     *
     * @param array $product_ids Array of product IDs to warm up
     * @param array $country_codes Array of country codes to warm up
     * @return int Number of cache entries created
     */
    public function warm_cache($product_ids, $country_codes) {
        $cache_entries = 0;

        foreach ($product_ids as $product_id) {
            foreach ($country_codes as $country_code) {
                // Skip if already cached
                if ($this->get_cached_price($product_id, $country_code) !== false) {
                    continue;
                }

                // Calculate and cache the price
                $this->get_price_adjustment($product_id, $country_code);
                $cache_entries++;
            }
        }

        return $cache_entries;
    }

    /**
     * Preload pricing rules for multiple products and countries
     *
     * @param array $product_ids Array of product IDs
     * @param array $country_codes Array of country codes
     * @return int Number of rules preloaded
     */
    public function preload_pricing_rules($product_ids, $country_codes) {
        $preloaded = 0;

        foreach ($product_ids as $product_id) {
            foreach ($country_codes as $country_code) {
                // Skip if already cached
                if ($this->get_cached_rule($product_id, $country_code) !== false) {
                    continue;
                }

                // Find and cache the rule
                $this->find_applicable_rule($product_id, $country_code);
                $preloaded++;
            }
        }

        return $preloaded;
    }

    /**
     * Check if product has country-specific pricing
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return bool True if has specific pricing, false otherwise
     */
    public function has_country_pricing($product_id, $country_code = null) {
        if (!$country_code) {
            $country_code = $this->country_detector->detect_country();
        }

        $rule = $this->find_applicable_rule($product_id, $country_code);
        return $rule !== null;
    }

    /**
     * Get all applicable rules for a product (for debugging/admin purposes)
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return array All applicable rules with priority info
     */
    public function get_all_applicable_rules($product_id, $country_code = null) {
        if (!$country_code) {
            $country_code = $this->country_detector->detect_country();
        }

        $rules = [];

        // Product rule
        $product_rule = $this->database->get_pricing_rule_by_criteria('product', $product_id, $country_code);
        if ($product_rule) {
            $rules[] = [
                'rule' => $product_rule,
                'priority' => self::RULE_PRIORITY['product'],
                'active' => true
            ];
        }

        // Category rules
        $category_ids = $this->get_product_category_ids($product_id);
        foreach ($category_ids as $category_id) {
            $category_rule = $this->database->get_pricing_rule_by_criteria('category', $category_id, $country_code);
            if ($category_rule) {
                $rules[] = [
                    'rule' => $category_rule,
                    'priority' => self::RULE_PRIORITY['category'],
                    'active' => !$product_rule // Only active if no product rule
                ];
            }
        }

        // Global rule
        $global_rule = $this->database->get_pricing_rule_by_criteria('global', null, $country_code);
        if ($global_rule) {
            $rules[] = [
                'rule' => $global_rule,
                'priority' => self::RULE_PRIORITY['global'],
                'active' => !$product_rule && empty(array_filter($rules, function($r) { return $r['rule']->rule_type === 'category'; }))
            ];
        }

        // Sort by priority (highest first)
        usort($rules, function($a, $b) {
            return $b['priority'] - $a['priority'];
        });

        return $rules;
    }

    /**
     * Validate pricing rule data
     *
     * @param array $rule_data Rule data to validate
     * @return array Validation result with errors
     */
    public function validate_pricing_rule($rule_data) {
        $errors = [];

        // Required fields
        $required_fields = ['rule_type', 'country_code', 'adjustment_type', 'adjustment_value'];
        foreach ($required_fields as $field) {
            if (!isset($rule_data[$field]) || $rule_data[$field] === '') {
                $errors[] = "Field '{$field}' is required";
            }
        }

        // Validate rule type
        if (isset($rule_data['rule_type']) && !array_key_exists($rule_data['rule_type'], self::RULE_PRIORITY)) {
            $errors[] = "Invalid rule type: {$rule_data['rule_type']}";
        }

        // Validate country code
        if (isset($rule_data['country_code']) && !$this->country_detector->is_valid_country_code($rule_data['country_code'])) {
            $errors[] = "Invalid country code: {$rule_data['country_code']}";
        }

        // Validate adjustment type
        if (isset($rule_data['adjustment_type']) && !in_array($rule_data['adjustment_type'], ['fixed', 'percentage'])) {
            $errors[] = "Invalid adjustment type: {$rule_data['adjustment_type']}";
        }

        // Validate adjustment value
        if (isset($rule_data['adjustment_value'])) {
            $value = floatval($rule_data['adjustment_value']);
            if ($rule_data['adjustment_type'] === 'percentage' && ($value < -100 || $value > 1000)) {
                $errors[] = "Percentage adjustment must be between -100% and 1000%";
            }
        }

        // Validate object_id for non-global rules
        if (isset($rule_data['rule_type']) && $rule_data['rule_type'] !== 'global') {
            if (!isset($rule_data['object_id']) || intval($rule_data['object_id']) <= 0) {
                $errors[] = "Object ID is required for {$rule_data['rule_type']} rules";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Apply currency conversion to price result
     *
     * @param array $price_result Price calculation result
     * @param string $country_code Country code
     * @return array Updated price result with currency conversion
     */
    private function apply_currency_conversion($price_result, $country_code) {
        // Check if currency conversion is enabled
        if (!get_option('pbc_enable_currency_conversion', false)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC Currency Conversion Disabled");
            }
            return $price_result;
        }

        // Get target currency for country
        $target_currency = $this->get_country_currency($country_code);
        $base_currency = get_woocommerce_currency();

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC Currency Conversion Check: {$base_currency} -> {$target_currency} for country {$country_code}");
        }

        // Skip conversion if same currency
        if ($target_currency === $base_currency) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC Currency Conversion Skipped: Same currency ({$base_currency})");
            }
            return $price_result;
        }

        try {
            // Convert original price
            $converted_original = $this->currency_exchange->convert_amount(
                $price_result['original_price'],
                $base_currency,
                $target_currency
            );

            // Convert adjusted price
            $converted_adjusted = $this->currency_exchange->convert_amount(
                $price_result['adjusted_price'],
                $base_currency,
                $target_currency
            );

            if ($converted_original !== false && $converted_adjusted !== false) {
                // Update price result with converted values
                $price_result['original_price_base_currency'] = $price_result['original_price'];
                $price_result['adjusted_price_base_currency'] = $price_result['adjusted_price'];
                $price_result['base_currency'] = $base_currency;

                $price_result['original_price'] = $converted_original;
                $price_result['adjusted_price'] = $converted_adjusted;
                $price_result['adjustment_amount'] = $converted_adjusted - $converted_original;
                $price_result['currency_code'] = $target_currency;
                $price_result['currency_converted'] = true;

                // Debug logging
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("PBC Currency Conversion SUCCESS: {$price_result['adjusted_price_base_currency']} {$base_currency} -> {$converted_adjusted} {$target_currency} for country {$country_code}");
                }

                // Log currency conversion
                $this->logger->log_currency_conversion(
                    $price_result['original_price_base_currency'],
                    $base_currency,
                    $target_currency,
                    $converted_original,
                    $this->currency_exchange->get_exchange_rate($base_currency, $target_currency)
                );
            } else {
                // Debug logging for conversion failure
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("PBC Currency Conversion FAILED: Could not convert {$base_currency} to {$target_currency} for country {$country_code}");
                    error_log("PBC Currency Conversion Details: original_converted=" . ($converted_original !== false ? $converted_original : 'FAILED') . ", adjusted_converted=" . ($converted_adjusted !== false ? $converted_adjusted : 'FAILED'));
                }
            }

        } catch (Exception $e) {
            // Debug logging for conversion errors
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("PBC Currency Conversion Error: " . $e->getMessage() . " for {$base_currency} to {$target_currency}");
            }

            // Log conversion error but don't fail the price calculation
            $this->error_handler->handle_currency_error($e, [
                'country_code' => $country_code,
                'base_currency' => $base_currency,
                'target_currency' => $target_currency,
                'function' => __FUNCTION__
            ]);
        }

        return $price_result;
    }

    /**
     * Get currency code for a country
     *
     * @param string $country_code Country code
     * @return string Currency code
     */
    public function get_country_currency($country_code) {
        // Check for custom currency mapping first
        $custom_mappings = get_option('pbc_country_currency_mappings', []);
        if (isset($custom_mappings[$country_code])) {
            return $custom_mappings[$country_code];
        }

        // Default country to currency mapping
        $currency_map = $this->get_default_currency_mappings();
        
        return isset($currency_map[$country_code]) ? $currency_map[$country_code] : get_woocommerce_currency();
    }

    /**
     * Get default country to currency mappings
     *
     * @return array Country to currency mappings
     */
    private function get_default_currency_mappings() {
        return [
            'US' => 'USD', 'CA' => 'CAD', 'GB' => 'GBP', 'AU' => 'AUD', 'NZ' => 'NZD',
            'JP' => 'JPY', 'CN' => 'CNY', 'IN' => 'INR', 'KR' => 'KRW', 'SG' => 'SGD',
            'HK' => 'HKD', 'TH' => 'THB', 'MY' => 'MYR', 'ID' => 'IDR', 'PH' => 'PHP',
            'VN' => 'VND', 'TW' => 'TWD', 'BD' => 'BDT', 'PK' => 'PKR', 'LK' => 'LKR',
            'AT' => 'EUR', 'BE' => 'EUR', 'BG' => 'BGN', 'HR' => 'HRK', 'CY' => 'EUR',
            'CZ' => 'CZK', 'DK' => 'DKK', 'EE' => 'EUR', 'FI' => 'EUR', 'FR' => 'EUR',
            'DE' => 'EUR', 'GR' => 'EUR', 'HU' => 'HUF', 'IE' => 'EUR', 'IT' => 'EUR',
            'LV' => 'EUR', 'LT' => 'EUR', 'LU' => 'EUR', 'MT' => 'EUR', 'NL' => 'EUR',
            'PL' => 'PLN', 'PT' => 'EUR', 'RO' => 'RON', 'SK' => 'EUR', 'SI' => 'EUR',
            'ES' => 'EUR', 'SE' => 'SEK', 'CH' => 'CHF', 'NO' => 'NOK', 'IS' => 'ISK',
            'RU' => 'RUB', 'TR' => 'TRY', 'UA' => 'UAH', 'BY' => 'BYN', 'MD' => 'MDL',
            'MX' => 'MXN', 'BR' => 'BRL', 'AR' => 'ARS', 'CL' => 'CLP', 'CO' => 'COP',
            'PE' => 'PEN', 'UY' => 'UYU', 'PY' => 'PYG', 'BO' => 'BOB', 'EC' => 'USD',
            'VE' => 'VES', 'GY' => 'GYD', 'SR' => 'SRD', 'FK' => 'FKP', 'ZA' => 'ZAR',
            'EG' => 'EGP', 'MA' => 'MAD', 'TN' => 'TND', 'DZ' => 'DZD', 'LY' => 'LYD',
            'SD' => 'SDG', 'ET' => 'ETB', 'KE' => 'KES', 'UG' => 'UGX', 'TZ' => 'TZS',
            'RW' => 'RWF', 'BI' => 'BIF', 'DJ' => 'DJF', 'SO' => 'SOS', 'ER' => 'ERN',
            'SS' => 'SSP', 'CF' => 'XAF', 'TD' => 'XAF', 'CM' => 'XAF', 'GQ' => 'XAF',
            'GA' => 'XAF', 'CG' => 'XAF', 'AO' => 'AOA', 'CD' => 'CDF', 'ZM' => 'ZMW',
            'ZW' => 'ZWL', 'BW' => 'BWP', 'SZ' => 'SZL', 'LS' => 'LSL', 'MZ' => 'MZN',
            'MG' => 'MGA', 'MU' => 'MUR', 'SC' => 'SCR', 'KM' => 'KMF', 'YT' => 'EUR',
            'RE' => 'EUR', 'SH' => 'SHP', 'ST' => 'STN', 'CV' => 'CVE', 'GW' => 'XOF',
            'GN' => 'GNF', 'SL' => 'SLL', 'LR' => 'LRD', 'CI' => 'XOF', 'GH' => 'GHS',
            'TG' => 'XOF', 'BJ' => 'XOF', 'NE' => 'XOF', 'BF' => 'XOF', 'ML' => 'XOF',
            'SN' => 'XOF', 'MR' => 'MRU', 'GM' => 'GMD', 'IL' => 'ILS', 'PS' => 'ILS',
            'JO' => 'JOD', 'LB' => 'LBP', 'SY' => 'SYP', 'IQ' => 'IQD', 'SA' => 'SAR',
            'YE' => 'YER', 'OM' => 'OMR', 'AE' => 'AED', 'QA' => 'QAR', 'BH' => 'BHD',
            'KW' => 'KWD', 'IR' => 'IRR', 'AF' => 'AFN', 'PK' => 'PKR', 'UZ' => 'UZS',
            'TJ' => 'TJS', 'KG' => 'KGS', 'KZ' => 'KZT', 'TM' => 'TMT', 'AZ' => 'AZN',
            'GE' => 'GEL', 'AM' => 'AMD', 'MN' => 'MNT', 'KP' => 'KPW', 'LA' => 'LAK',
            'KH' => 'KHR', 'MM' => 'MMK', 'BT' => 'BTN', 'NP' => 'NPR', 'MV' => 'MVR'
        ];
    }

    /**
     * Get pricing statistics for admin dashboard
     *
     * @return array Pricing statistics
     */
    public function get_pricing_stats() {
        global $wpdb;
        
        $pricing_table = $this->database->get_pricing_rules_table();
        
        $stats = [
            'total_rules' => 0,
            'active_rules' => 0,
            'rules_by_type' => [
                'global' => 0,
                'category' => 0,
                'product' => 0
            ],
            'countries_with_rules' => 0,
            'cache_entries' => [
                'price_cache' => 0,
                'rules_cache' => 0,
                'batch_cache' => 0,
                'total' => 0
            ]
        ];

        // Get rule counts
        $rule_counts = $wpdb->get_results(
            "SELECT rule_type, is_active, COUNT(*) as count 
             FROM {$pricing_table} 
             GROUP BY rule_type, is_active"
        );

        foreach ($rule_counts as $count) {
            $stats['total_rules'] += $count->count;
            if ($count->is_active) {
                $stats['active_rules'] += $count->count;
                $stats['rules_by_type'][$count->rule_type] += $count->count;
            }
        }

        // Get unique countries count
        $countries_count = $wpdb->get_var(
            "SELECT COUNT(DISTINCT country_code) FROM {$pricing_table} WHERE is_active = 1"
        );
        $stats['countries_with_rules'] = intval($countries_count);

        // Get cache entries count by type
        $price_cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::CACHE_PREFIX . '%'
            )
        );
        $stats['cache_entries']['price_cache'] = intval($price_cache_count);

        $rules_cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::RULES_CACHE_PREFIX . '%'
            )
        );
        $stats['cache_entries']['rules_cache'] = intval($rules_cache_count);

        $batch_cache_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::BATCH_CACHE_PREFIX . '%'
            )
        );
        $stats['cache_entries']['batch_cache'] = intval($batch_cache_count);

        $stats['cache_entries']['total'] = $stats['cache_entries']['price_cache'] + 
                                          $stats['cache_entries']['rules_cache'] + 
                                          $stats['cache_entries']['batch_cache'];

        return $stats;
    }

    /**
     * Invalidate cache when pricing rules change
     *
     * @param string $rule_type Rule type that changed
     * @param int|null $object_id Object ID that changed
     * @param string|null $country_code Country code that changed
     */
    public function invalidate_cache_on_rule_change($rule_type, $object_id = null, $country_code = null) {
        global $wpdb;

        // Clear all batch cache as it may be affected
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . self::BATCH_CACHE_PREFIX . '%'
            )
        );

        if ($rule_type === 'product' && $object_id) {
            // Clear cache for specific product
            $this->clear_price_cache($object_id);
            $this->clear_rule_cache($object_id);
        } elseif ($rule_type === 'category' && $object_id) {
            // Clear cache for all products in category
            $this->clear_category_cache($object_id);
        } elseif ($rule_type === 'global') {
            // Clear all cache as global rules affect everything
            $this->clear_all_price_cache();
        }

        // Clear country-specific cache if specified
        if ($country_code) {
            $this->clear_country_cache($country_code);
        }
    }

    /**
     * Clear cache for all products in a category
     *
     * @param int $category_id Category ID
     */
    private function clear_category_cache($category_id) {
        // Get all products in category
        $product_ids = get_posts([
            'post_type' => 'product',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                ]
            ]
        ]);

        foreach ($product_ids as $product_id) {
            $this->clear_price_cache($product_id);
            $this->clear_rule_cache($product_id);
        }
    }

    /**
     * Clear cache for specific country
     *
     * @param string $country_code Country code
     */
    private function clear_country_cache($country_code) {
        global $wpdb;
        
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_' . self::CACHE_PREFIX . '%_' . $country_code,
                '_transient_' . self::RULES_CACHE_PREFIX . '%_' . $country_code
            )
        );
    }
}