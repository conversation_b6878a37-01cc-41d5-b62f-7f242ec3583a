<!DOCTYPE html>
<html>
  <head>
    <title>Results Visibility Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ccc;
      }
      .button {
        padding: 8px 16px;
        background: #0073aa;
        color: white;
        border: none;
        cursor: pointer;
        margin: 5px;
      }
      .button:hover {
        background: #005a87;
      }
      .pbc-test-results {
        margin-top: 15px;
        padding: 10px;
        background: #f9f9f9;
        border: 1px solid #ddd;
      }
      .debug {
        background: #f0f0f0;
        padding: 10px;
        margin: 10px 0;
        font-family: monospace;
      }
    </style>
  </head>
  <body>
    <h1>Results Visibility Test</h1>
    <p>This tests the visibility behavior of the results element.</p>

    <div class="test-section">
      <h2>Simulate Admin Interface</h2>

      <!-- Simulate the exact HTML structure from the admin -->
      <div>
        <label for="pbc_test_ip">Test IP Address:</label>
        <input
          type="text"
          id="pbc_test_ip"
          placeholder="*******"
          value="*******"
        />
        <button type="button" id="pbc_test_ip_detection" class="button">
          Test IP Detection
        </button>
      </div>

      <!-- This matches the exact HTML from the admin with inline style -->
      <div id="pbc_test_results" class="pbc-test-results" style="display: none">
        <h5>Test Results:</h5>
        <div id="pbc_test_output"></div>
      </div>

      <div class="debug">
        <h3>Debug Information</h3>
        <div id="debug_output"></div>
      </div>
    </div>

    <div class="test-section">
      <h2>Manual Tests</h2>
      <button id="test_hide" class="button">Test Hide</button>
      <button id="test_show" class="button">Test Show</button>
      <button id="test_add_class" class="button">Test Add Class 'show'</button>
      <button id="test_remove_class" class="button">
        Test Remove Class 'show'
      </button>
      <button id="check_visibility" class="button">Check Visibility</button>
    </div>

    <script>
      jQuery(document).ready(function ($) {
        console.log("Results visibility test loaded");

        // Simulate the scrollToResults function
        function scrollToResults() {
          var $results = $("#pbc_test_results");
          if ($results.length) {
            try {
              // Ensure the element is visible first
              if (!$results.is(":visible")) {
                $results.show();
                console.log("PBC: Made results element visible for scrolling");
              }

              var targetOffset = $results.offset().top - 100;
              console.log("PBC: Scrolling to results at offset:", targetOffset);
              $("html, body").animate(
                {
                  scrollTop: targetOffset,
                },
                500
              );
            } catch (e) {
              console.error("PBC: Error scrolling to results:", e);
            }
          } else {
            console.log("PBC: Results element not found");
          }
        }

        // Simulate the IP detection test
        $("#pbc_test_ip_detection").on("click", function (e) {
          e.preventDefault();
          console.log("PBC: IP detection button clicked");

          var $button = $(this);
          var $results = $("#pbc_test_results");
          var $output = $("#pbc_test_output");
          var testIp = $("#pbc_test_ip").val().trim();

          console.log("PBC: Starting IP detection test for:", testIp);
          console.log("PBC: Button element:", $button.length);
          console.log("PBC: Results element:", $results.length);
          console.log("PBC: Output element:", $output.length);
          console.log(
            "PBC: Results initially visible:",
            $results.is(":visible")
          );

          // Add loading state
          $button.addClass("loading").prop("disabled", true).text("Testing...");
          $results.hide();

          // Clear previous results
          $output.empty();

          // Simulate AJAX delay
          setTimeout(function () {
            // Simulate success response
            var resultHtml = '<div class="pbc-test-success">';
            resultHtml += "<h5>Detection Successful</h5>";
            resultHtml += '<div class="test-result-grid">';
            resultHtml +=
              '<div class="result-item"><strong>IP Address:</strong> ' +
              testIp +
              "</div>";
            resultHtml +=
              '<div class="result-item"><strong>Country:</strong> United States (US)</div>';
            resultHtml +=
              '<div class="result-item"><strong>Detection Method:</strong> ip</div>';
            resultHtml += "</div></div>";

            $output.html(resultHtml);

            console.log("PBC: About to show results and scroll");
            console.log(
              "PBC: Results visible before show():",
              $results.is(":visible")
            );

            $results.show();

            console.log(
              "PBC: Results visible after show():",
              $results.is(":visible")
            );

            scrollToResults();

            $button
              .removeClass("loading")
              .prop("disabled", false)
              .text("Test IP Detection");
          }, 1000);
        });

        // Manual test buttons
        $("#test_hide").on("click", function () {
          $("#pbc_test_results").hide();
          updateDebug();
        });

        $("#test_show").on("click", function () {
          $("#pbc_test_results").show();
          updateDebug();
        });

        $("#test_add_class").on("click", function () {
          $("#pbc_test_results").addClass("show");
          updateDebug();
        });

        $("#test_remove_class").on("click", function () {
          $("#pbc_test_results").removeClass("show");
          updateDebug();
        });

        $("#check_visibility").on("click", function () {
          updateDebug();
        });

        function updateDebug() {
          var $results = $("#pbc_test_results");
          var debug = "";
          debug +=
            "<p><strong>Element exists:</strong> " +
            ($results.length > 0) +
            "</p>";
          debug +=
            "<p><strong>Is visible:</strong> " +
            $results.is(":visible") +
            "</p>";
          debug +=
            "<p><strong>Display style:</strong> " +
            $results.css("display") +
            "</p>";
          debug +=
            '<p><strong>Has "show" class:</strong> ' +
            $results.hasClass("show") +
            "</p>";
          debug +=
            "<p><strong>Inline style:</strong> " +
            ($results.attr("style") || "none") +
            "</p>";
          debug +=
            "<p><strong>Computed display:</strong> " +
            window.getComputedStyle($results[0]).display +
            "</p>";

          $("#debug_output").html(debug);
          console.log("Debug info updated");
        }

        // Initial debug update
        updateDebug();
      });
    </script>
  </body>
</html>
