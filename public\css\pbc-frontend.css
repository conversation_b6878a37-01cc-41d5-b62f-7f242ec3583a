/**
 * Frontend CSS for Price by Country WooCommerce Plugin
 * Styles for price updates and notifications
 */

/* Loading overlay - optimized for performance */
.pbc-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
  will-change: opacity;
  backdrop-filter: blur(2px);
}

.pbc-loading-overlay.show {
  display: flex;
}

/* Loading spinner */
.pbc-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0073aa;
  border-radius: 50%;
  animation: pbc-spin 1s linear infinite;
}

@keyframes pbc-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Price update animation - GPU accelerated */
.pbc-price-updated {
  animation: pbc-price-highlight 1s ease-in-out;
  will-change: background-color;
}

@keyframes pbc-price-highlight {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #fff3cd;
  }
  100% {
    background-color: transparent;
  }
}

/* Lazy loading optimization */
.pbc-product-loading {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.pbc-product-loaded {
  opacity: 1;
}

/* Notifications */
.pbc-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: #fff;
  font-weight: 500;
  z-index: 10000;
  display: none;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pbc-notification-success {
  background-color: #28a745;
  border-left: 4px solid #1e7e34;
}

.pbc-notification-error {
  background-color: #dc3545;
  border-left: 4px solid #c82333;
}

.pbc-notification-info {
  background-color: #17a2b8;
  border-left: 4px solid #138496;
}

.pbc-notification-warning {
  background-color: #ffc107;
  color: #212529;
  border-left: 4px solid #e0a800;
}

/* Country selector styling */
.pbc-country-selector {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  font-size: 14px;
  min-width: 150px;
}

.pbc-country-selector:focus {
  outline: none;
  border-color: #0073aa;
  box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

/* Price display enhancements */
.price.pbc-country-price {
  position: relative;
}

.price.pbc-country-price::after {
  content: attr(data-country);
  font-size: 0.8em;
  color: #666;
  margin-left: 5px;
  font-weight: normal;
}

/* Checkout price update indicator */
.woocommerce-checkout .pbc-price-updating {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.woocommerce-checkout .pbc-price-updating::after {
  content: " (updating...)";
  font-size: 0.9em;
  color: #666;
  font-style: italic;
}

/* Cart price updates */
.woocommerce-cart .cart_item.pbc-updating {
  background-color: #f8f9fa;
  transition: background-color 0.3s ease;
}

/* Variable product price updates */
.single_variation .price.pbc-price-updated {
  border: 2px solid #0073aa;
  padding: 5px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .pbc-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .pbc-spinner {
    width: 30px;
    height: 30px;
    border-width: 3px;
  }

  .pbc-country-selector {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pbc-notification {
    border: 2px solid currentColor;
  }

  .pbc-price-updated {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pbc-spinner {
    animation: none;
    border-top-color: transparent;
  }

  .pbc-price-updated {
    animation: none;
    background-color: #fff3cd;
  }

  .pbc-notification {
    transition: none;
  }
}

/* Print styles */
@media print {
  .pbc-loading-overlay,
  .pbc-notification {
    display: none !important;
  }
}

/* Focus indicators for accessibility */
.pbc-country-selector:focus-visible {
  outline: 2px solid #0073aa;
  outline-offset: 2px;
}

/* Screen reader only content */
.pbc-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Price comparison styling */
.pbc-price-comparison {
  font-size: 0.9em;
  color: #666;
  margin-top: 5px;
}

.pbc-price-comparison .original-price {
  text-decoration: line-through;
  margin-right: 10px;
}

.pbc-price-comparison .savings {
  color: #28a745;
  font-weight: 500;
}

/* Country flag icons (if used) */
.pbc-country-flag {
  width: 20px;
  height: 15px;
  margin-right: 5px;
  vertical-align: middle;
}

/* Tooltip styling for price explanations */
.pbc-price-tooltip {
  position: relative;
  cursor: help;
}

.pbc-price-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.pbc-price-tooltip:hover::after {
  opacity: 1;
}

/* Error states */
.pbc-price-error {
  color: #dc3545;
  font-style: italic;
}

.pbc-price-error::before {
  content: "⚠ ";
  margin-right: 3px;
}

/* Success states */
.pbc-price-success {
  color: #28a745;
}

.pbc-price-success::before {
  content: "✓ ";
  margin-right: 3px;
}
/* Checkout-specific styles */
.pbc-checkout-notification {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-left: 4px solid #17a2b8;
  color: #0c5460;
  padding: 12px 15px;
  margin: 15px 0;
  border-radius: 4px;
  font-size: 14px;
}

.pbc-checkout-notification::before {
  content: "ℹ ";
  font-weight: bold;
  margin-right: 5px;
}

.woocommerce-checkout.pbc-updating {
  position: relative;
}

.woocommerce-checkout.pbc-updating::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 100;
  pointer-events: none;
}

.checkout-review-order-table.pbc-price-updating {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.pbc-updating-message {
  font-size: 0.9em;
  color: #666;
  font-style: italic;
  animation: pbc-pulse 1.5s ease-in-out infinite;
}

@keyframes pbc-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Checkout address change indicators */
.woocommerce-checkout #billing_country:focus,
.woocommerce-checkout #shipping_country:focus {
  border-color: #0073aa;
  box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

.woocommerce-checkout .country-changing {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  transition: all 0.3s ease;
}

/* Order review table updates */
.woocommerce-checkout-review-order-table .pbc-price-updated {
  background-color: #d4edda;
  transition: background-color 0.5s ease;
}

.woocommerce-checkout-review-order-table .cart_item.pbc-updating {
  background-color: #f8f9fa;
  opacity: 0.7;
}

/* Price change highlights in checkout */
.order-total .pbc-price-change {
  display: inline-block;
  padding: 2px 6px;
  background: #28a745;
  color: white;
  border-radius: 3px;
  font-size: 0.8em;
  margin-left: 5px;
  animation: pbc-highlight-fade 3s ease-out;
}

@keyframes pbc-highlight-fade {
  0% {
    background-color: #28a745;
  }
  100% {
    background-color: transparent;
    color: inherit;
  }
}

/* Responsive checkout styles */
@media (max-width: 768px) {
  .pbc-checkout-notification {
    margin: 10px 0;
    padding: 10px 12px;
    font-size: 13px;
  }

  .pbc-updating-message {
    display: block;
    margin-top: 5px;
  }

  .woocommerce-checkout.pbc-updating::after {
    background: rgba(255, 255, 255, 0.8);
  }
}

/* Accessibility improvements for checkout */
.pbc-checkout-notification[role="alert"] {
  /* Screen readers will announce this */
}

.pbc-updating-message[aria-live="polite"] {
  /* Screen readers will announce updates */
}

/* High contrast mode for checkout */
@media (prefers-contrast: high) {
  .pbc-checkout-notification {
    border: 2px solid #17a2b8;
    background: #fff;
  }

  .checkout-review-order-table.pbc-price-updating {
    border: 2px dashed #666;
  }
}

/* Price update animation */
.pbc-price-updating {
    opacity: 0.5;
    transition: opacity 0.2s ease-in-out;
}

.pbc-price-updated {
    animation: pbc-flash 1s ease-out;
}

@keyframes pbc-flash {
    0% {
        background-color: transparent;
    }
    25% {
        background-color: #c7e6c7; /* Light green flash */
    }
    100% {
        background-color: transparent;
    }
}
