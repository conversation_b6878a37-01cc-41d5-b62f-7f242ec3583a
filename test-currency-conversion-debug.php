<?php
/**
 * Debug script for currency conversion issues
 * 
 * This script will help identify why prices are not converting while currency symbols are changing
 */

// WordPress bootstrap
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

// Enable debug mode
if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}

echo "<h1>Price by Country - Currency Conversion Debug</h1>\n";
echo "<pre>\n";

// Check if plugin is active
if (!class_exists('PBC_Core')) {
    echo "ERROR: Price by Country plugin is not active!\n";
    exit;
}

// 1. Check current settings
echo "=== CURRENT SETTINGS ===\n";
echo "Currency Conversion Enabled: " . (get_option('pbc_enable_currency_conversion', false) ? 'YES' : 'NO') . "\n";
echo "Pricing Rules Enabled: " . (get_option('pbc_enable_pricing_rules', true) ? 'YES' : 'NO') . "\n";
echo "ExchangeRate API Key: " . (get_option('pbc_exchangerate_api_key', '') ? 'SET (' . strlen(get_option('pbc_exchangerate_api_key', '')) . ' chars)' : 'NOT SET') . "\n";
echo "Base Currency: " . get_option('woocommerce_currency', 'USD') . "\n";
echo "Exchange Providers: " . print_r(get_option('pbc_exchange_providers', ['exchangerate_api', 'ecb']), true) . "\n";

// 2. Test country detection
echo "\n=== COUNTRY DETECTION ===\n";
try {
    $country_detector = new PBC_Country_Detector();
    $detected_country = $country_detector->detect_country();
    echo "Detected Country: " . ($detected_country ?: 'NONE') . "\n";
    
    // Force UK detection for testing
    $country_detector->set_forced_country('GB');
    $forced_country = $country_detector->detect_country();
    echo "Forced Country (GB): " . $forced_country . "\n";
} catch (Exception $e) {
    echo "ERROR in country detection: " . $e->getMessage() . "\n";
}

// 3. Test currency mapping
echo "\n=== CURRENCY MAPPING ===\n";
try {
    $pricing_engine = new PBC_Pricing_Engine();
    $gb_currency = $pricing_engine->get_country_currency('GB');
    echo "GB Currency: " . $gb_currency . "\n";
    $us_currency = $pricing_engine->get_country_currency('US');
    echo "US Currency: " . $us_currency . "\n";
} catch (Exception $e) {
    echo "ERROR in currency mapping: " . $e->getMessage() . "\n";
}

// 4. Test exchange rate API
echo "\n=== EXCHANGE RATE API TEST ===\n";
try {
    $currency_exchange = new PBC_Currency_Exchange();
    
    // Test USD to GBP conversion
    echo "Testing USD to GBP exchange rate...\n";
    $usd_to_gbp_rate = $currency_exchange->get_exchange_rate('USD', 'GBP');
    echo "USD to GBP Rate: " . ($usd_to_gbp_rate !== false ? $usd_to_gbp_rate : 'FAILED') . "\n";
    
    if ($usd_to_gbp_rate !== false) {
        // Test amount conversion
        $test_amount = 100.00;
        $converted_amount = $currency_exchange->convert_amount($test_amount, 'USD', 'GBP');
        echo "Convert $test_amount USD to GBP: " . ($converted_amount !== false ? $converted_amount : 'FAILED') . "\n";
    }
    
    // Test provider connectivity
    echo "\nTesting exchange rate providers:\n";
    $providers = ['exchangerate_api', 'ecb', 'bank_of_canada'];
    foreach ($providers as $provider) {
        $test_result = $currency_exchange->test_provider($provider);
        echo "- $provider: " . ($test_result['success'] ? 'SUCCESS' : 'FAILED') . 
             " (Response time: " . ($test_result['response_time'] ?? 'N/A') . "ms)\n";
        if (!$test_result['success'] && isset($test_result['error'])) {
            echo "  Error: " . $test_result['error'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR in exchange rate testing: " . $e->getMessage() . "\n";
}

// 5. Test full price adjustment flow
echo "\n=== PRICE ADJUSTMENT TEST ===\n";
try {
    // Get a sample product
    $products = get_posts([
        'post_type' => 'product',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ]);
    
    if (empty($products)) {
        echo "No products found for testing\n";
    } else {
        $product_id = $products[0]->ID;
        $product = wc_get_product($product_id);
        echo "Testing with Product ID: $product_id\n";
        echo "Product Name: " . $product->get_name() . "\n";
        echo "Original Price: " . $product->get_price() . " " . get_woocommerce_currency() . "\n";
        
        // Test price adjustment for GB
        $pricing_engine = new PBC_Pricing_Engine();
        $price_result = $pricing_engine->get_price_adjustment($product_id, 'GB', floatval($product->get_price()));
        
        echo "\nPrice adjustment result for GB:\n";
        echo "- Original Price: " . $price_result['original_price'] . "\n";
        echo "- Adjusted Price: " . $price_result['adjusted_price'] . "\n";
        echo "- Currency Code: " . ($price_result['currency_code'] ?? 'NOT SET') . "\n";
        echo "- Currency Converted: " . (isset($price_result['currency_converted']) && $price_result['currency_converted'] ? 'YES' : 'NO') . "\n";
        echo "- Rule Source: " . $price_result['rule_source'] . "\n";
        
        if (isset($price_result['base_currency'])) {
            echo "- Base Currency: " . $price_result['base_currency'] . "\n";
            echo "- Original Price (Base): " . $price_result['original_price_base_currency'] . "\n";
            echo "- Adjusted Price (Base): " . $price_result['adjusted_price_base_currency'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR in price adjustment testing: " . $e->getMessage() . "\n";
}

// 6. Test direct BDT to GBP conversion
echo "\n=== DIRECT BDT TO GBP TEST ===\n";
try {
    $currency_exchange = new PBC_Currency_Exchange();

    // Test BDT to GBP conversion specifically
    echo "Testing BDT to GBP conversion...\n";
    $bdt_to_gbp_rate = $currency_exchange->get_exchange_rate('BDT', 'GBP');
    echo "BDT to GBP Rate: " . ($bdt_to_gbp_rate !== false ? $bdt_to_gbp_rate : 'FAILED') . "\n";

    if ($bdt_to_gbp_rate !== false) {
        // Test converting 1200 BDT to GBP
        $test_amount = 1200.00;
        $converted_amount = $currency_exchange->convert_amount($test_amount, 'BDT', 'GBP');
        echo "Convert {$test_amount} BDT to GBP: " . ($converted_amount !== false ? number_format($converted_amount, 2) : 'FAILED') . "\n";
    }

} catch (Exception $e) {
    echo "ERROR in BDT to GBP testing: " . $e->getMessage() . "\n";
}

echo "\n=== RECOMMENDATIONS ===\n";
echo "1. Enable WordPress debug logging: define('WP_DEBUG', true); define('WP_DEBUG_LOG', true);\n";
echo "2. Check debug.log file for detailed currency conversion logs\n";
echo "3. Ensure currency conversion is enabled in plugin settings\n";
echo "4. Consider getting an ExchangeRate-API key for better reliability\n";
echo "5. Clear all caches after making changes\n";

echo "\n=== DEBUG COMPLETE ===\n";
echo "</pre>\n";
?>
