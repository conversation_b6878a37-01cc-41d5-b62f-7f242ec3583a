<?php
/**
 * Test AJAX endpoints for country detection
 */

// Include WordPress
require_once('wp-config.php');

// Check if we're in admin and user has permissions
if (!is_admin() || !current_user_can('manage_woocommerce')) {
    wp_die('Access denied');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>AJAX Endpoints Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .button { padding: 8px 16px; background: #0073aa; color: white; border: none; cursor: pointer; }
        .button:hover { background: #005a87; }
        .results { margin-top: 15px; padding: 10px; background: #f9f9f9; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>AJAX Endpoints Test</h1>
    
    <div class="test-section">
        <h2>Test IP Detection AJAX</h2>
        <button id="test-ip-ajax" class="button">Test IP Detection (*******)</button>
        <div id="ip-ajax-results" class="results" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test User Detection AJAX</h2>
        <button id="test-user-ajax" class="button">Test User Detection (User ID: 1)</button>
        <div id="user-ajax-results" class="results" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Debug Information</h2>
        <p><strong>AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?></p>
        <p><strong>Nonce:</strong> <?php echo wp_create_nonce('pbc_admin_nonce'); ?></p>
        <p><strong>Current User:</strong> <?php echo wp_get_current_user()->user_login; ?></p>
        <p><strong>Can Manage WooCommerce:</strong> <?php echo current_user_can('manage_woocommerce') ? 'Yes' : 'No'; ?></p>
    </div>

    <script>
    jQuery(document).ready(function($) {
        console.log('AJAX Test Page Loaded');
        
        // Test IP Detection AJAX
        $('#test-ip-ajax').on('click', function() {
            console.log('Testing IP Detection AJAX');
            var $results = $('#ip-ajax-results');
            var $button = $(this);
            
            $button.prop('disabled', true).text('Testing...');
            $results.html('<div>Testing IP detection AJAX...</div>').show();
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'pbc_test_ip_detection',
                    test_ip: '*******',
                    nonce: '<?php echo wp_create_nonce('pbc_admin_nonce'); ?>'
                },
                success: function(response) {
                    console.log('IP AJAX Success:', response);
                    if (response.success) {
                        $results.html(
                            '<div class="success">' +
                            '<h3>Success!</h3>' +
                            '<p><strong>IP:</strong> ' + response.data.ip_address + '</p>' +
                            '<p><strong>Country:</strong> ' + response.data.country_name + ' (' + response.data.country_code + ')</p>' +
                            '<p><strong>Method:</strong> ' + response.data.detection_method + '</p>' +
                            '<p><strong>Message:</strong> ' + response.data.message + '</p>' +
                            '<h4>Full Response:</h4>' +
                            '<pre>' + JSON.stringify(response, null, 2) + '</pre>' +
                            '</div>'
                        );
                    } else {
                        $results.html(
                            '<div class="error">' +
                            '<h3>Error</h3>' +
                            '<p>' + (response.data || 'Unknown error') + '</p>' +
                            '<h4>Full Response:</h4>' +
                            '<pre>' + JSON.stringify(response, null, 2) + '</pre>' +
                            '</div>'
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error('IP AJAX Error:', xhr, status, error);
                    $results.html(
                        '<div class="error">' +
                        '<h3>AJAX Error</h3>' +
                        '<p><strong>Status:</strong> ' + status + '</p>' +
                        '<p><strong>Error:</strong> ' + error + '</p>' +
                        '<p><strong>Response Text:</strong> ' + xhr.responseText + '</p>' +
                        '</div>'
                    );
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test IP Detection (*******)');
                }
            });
        });
        
        // Test User Detection AJAX
        $('#test-user-ajax').on('click', function() {
            console.log('Testing User Detection AJAX');
            var $results = $('#user-ajax-results');
            var $button = $(this);
            
            $button.prop('disabled', true).text('Testing...');
            $results.html('<div>Testing user detection AJAX...</div>').show();
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'pbc_test_user_detection',
                    user_id: 1,
                    nonce: '<?php echo wp_create_nonce('pbc_admin_nonce'); ?>'
                },
                success: function(response) {
                    console.log('User AJAX Success:', response);
                    if (response.success) {
                        var html = '<div class="success"><h3>Success!</h3>';
                        html += '<p><strong>User ID:</strong> ' + response.data.user_id + '</p>';
                        html += '<p><strong>Message:</strong> ' + response.data.message + '</p>';
                        html += '<h4>Detection Results:</h4>';
                        
                        if (response.data.results && response.data.results.length > 0) {
                            response.data.results.forEach(function(result) {
                                html += '<p><strong>' + result.method.charAt(0).toUpperCase() + result.method.slice(1) + ':</strong> ';
                                html += result.country_name + ' (' + result.country_code + ')</p>';
                            });
                        }
                        
                        html += '<h4>Full Response:</h4>';
                        html += '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                        html += '</div>';
                        
                        $results.html(html);
                    } else {
                        $results.html(
                            '<div class="error">' +
                            '<h3>Error</h3>' +
                            '<p>' + (response.data || 'Unknown error') + '</p>' +
                            '<h4>Full Response:</h4>' +
                            '<pre>' + JSON.stringify(response, null, 2) + '</pre>' +
                            '</div>'
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error('User AJAX Error:', xhr, status, error);
                    $results.html(
                        '<div class="error">' +
                        '<h3>AJAX Error</h3>' +
                        '<p><strong>Status:</strong> ' + status + '</p>' +
                        '<p><strong>Error:</strong> ' + error + '</p>' +
                        '<p><strong>Response Text:</strong> ' + xhr.responseText + '</p>' +
                        '</div>'
                    );
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test User Detection (User ID: 1)');
                }
            });
        });
    });
    </script>
</body>
</html>