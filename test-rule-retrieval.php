<?php
/**
 * Test if saved rules can be retrieved from the database
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h2>Testing Rule Retrieval</h2>\n";

// Check if PBC is loaded
if (!class_exists('PBC_Core')) {
    echo "<p style='color: red;'>ERROR: PBC_Core class not found.</p>\n";
    exit;
}

$pbc_core = PBC_Core::get_instance();
if (!$pbc_core || !$pbc_core->database) {
    echo "<p style='color: red;'>ERROR: PBC Database not available.</p>\n";
    exit;
}

echo "<p style='color: green;'>✓ PBC Database available</p>\n";

// Test product ID from the logs
$test_product_id = 459;

echo "<h3>Testing Rule Retrieval for Product ID: $test_product_id</h3>\n";

try {
    // Test the method that's used in the admin interface
    $rules = $pbc_core->database->get_pricing_rules_by_type('product', $test_product_id);
    
    echo "<p style='color: green;'>✓ Retrieved " . count($rules) . " rules</p>\n";
    
    if (!empty($rules)) {
        echo "<h4>Rule Details:</h4>\n";
        foreach ($rules as $rule) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0;'>\n";
            echo "<p><strong>Rule ID:</strong> {$rule->id}</p>\n";
            echo "<p><strong>Country:</strong> {$rule->country_code}</p>\n";
            echo "<p><strong>Type:</strong> {$rule->adjustment_type}</p>\n";
            echo "<p><strong>Value:</strong> {$rule->adjustment_value}</p>\n";
            echo "<p><strong>Active:</strong> " . ($rule->is_active ? 'Yes' : 'No') . "</p>\n";
            echo "<p><strong>Created:</strong> {$rule->created_at}</p>\n";
            echo "</div>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No rules found for this product</p>\n";
    }
    
    // Test with different parameters
    echo "<h4>Testing with include_inactive = false:</h4>\n";
    $active_rules = $pbc_core->database->get_pricing_rules_by_type('product', $test_product_id, true);
    echo "<p>Active rules only: " . count($active_rules) . "</p>\n";
    
    echo "<h4>Testing with include_inactive = true:</h4>\n";
    $all_rules = $pbc_core->database->get_pricing_rules_by_type('product', $test_product_id, false);
    echo "<p>All rules (including inactive): " . count($all_rules) . "</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>ERROR: Exception during rule retrieval: " . $e->getMessage() . "</p>\n";
    echo "<p>Stack trace:</p><pre>" . $e->getTraceAsString() . "</pre>\n";
}

// Test direct database query
echo "<h3>Testing Direct Database Query</h3>\n";

try {
    global $wpdb;
    $table_name = $wpdb->prefix . 'pbc_pricing_rules';
    
    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    echo "<p>Table exists: " . ($table_exists ? 'Yes' : 'No') . "</p>\n";
    
    if ($table_exists) {
        // Get all rules for this product
        $direct_rules = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE rule_type = 'product' AND object_id = %d ORDER BY created_at DESC",
            $test_product_id
        ));
        
        echo "<p style='color: green;'>✓ Direct query found " . count($direct_rules) . " rules</p>\n";
        
        if (!empty($direct_rules)) {
            echo "<h4>Direct Query Results:</h4>\n";
            foreach ($direct_rules as $rule) {
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0;'>\n";
                echo "<p><strong>ID:</strong> {$rule->id}</p>\n";
                echo "<p><strong>Country:</strong> {$rule->country_code}</p>\n";
                echo "<p><strong>Type:</strong> {$rule->adjustment_type}</p>\n";
                echo "<p><strong>Value:</strong> {$rule->adjustment_value}</p>\n";
                echo "<p><strong>Active:</strong> {$rule->is_active}</p>\n";
                echo "<p><strong>Created:</strong> {$rule->created_at}</p>\n";
                echo "</div>\n";
            }
        }
        
        // Get total count of all rules
        $total_rules = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo "<p>Total rules in database: $total_rules</p>\n";
        
        // Get recent rules
        $recent_rules = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC LIMIT 5");
        echo "<h4>5 Most Recent Rules:</h4>\n";
        foreach ($recent_rules as $rule) {
            echo "<p>ID {$rule->id}: {$rule->rule_type} {$rule->object_id} - {$rule->country_code} {$rule->adjustment_value} (created: {$rule->created_at})</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>ERROR: Database query failed: " . $e->getMessage() . "</p>\n";
}

echo "<h3>Test Complete</h3>\n";
?>